
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ubuntu-deployment
  labels:
    app: ubuntu
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ubuntu
  template:
    metadata:
      labels:
        app: ubuntu
    spec:
      containers:
      - name: ubuntu
        image: vsystems/ubuntu-ssh
        resources:
          limits:
            memory: 256Gi
        ports:
        # TCP Ports
        - containerPort: 8900
          hostPort: 8900
          protocol: TCP
        - containerPort: 8001
          hostPort: 8001
          protocol: TCP
        - containerPort: 8901
          hostPort: 8901
          protocol: TCP
        - containerPort: 8902
          hostPort: 8902
          protocol: TCP
        - containerPort: 9900
          hostPort: 9900
          protocol: TCP
        - containerPort: 8000
          hostPort: 8000
          protocol: TCP
        # UDP Ports (8000-8020)
        - containerPort: 8000
          hostPort: 8000
          protocol: UDP
        - containerPort: 8001
          hostPort: 8001
          protocol: UDP
        - containerPort: 8002
          hostPort: 8002
          protocol: UDP
        - containerPort: 8003
          hostPort: 8003
          protocol: UDP
        - containerPort: 8004
          hostPort: 8004
          protocol: UDP
        - containerPort: 8005
          hostPort: 8005
          protocol: UDP
        - containerPort: 8006
          hostPort: 8006
          protocol: UDP
        - containerPort: 8007
          hostPort: 8007
          protocol: UDP
        - containerPort: 8008
          hostPort: 8008
          protocol: UDP
        - containerPort: 8009
          hostPort: 8009
          protocol: UDP
        - containerPort: 8010
          hostPort: 8010
          protocol: UDP
        - containerPort: 8011
          hostPort: 8011
          protocol: UDP
        - containerPort: 8012
          hostPort: 8012
          protocol: UDP
        - containerPort: 8013
          hostPort: 8013
          protocol: UDP
        - containerPort: 8014
          hostPort: 8014
          protocol: UDP
        - containerPort: 8015
          hostPort: 8015
          protocol: UDP
        - containerPort: 8016
          hostPort: 8016
          protocol: UDP
        - containerPort: 8017
          hostPort: 8017
          protocol: UDP
        - containerPort: 8018
          hostPort: 8018
          protocol: UDP
        - containerPort: 8019
          hostPort: 8019
          protocol: UDP
        - containerPort: 8020
          hostPort: 8020
          protocol: UDP
        volumeMounts:
        - name: solana-volume
          mountPath: /mnt
      volumes:
      - name: solana-volume
        hostPath:
          path: /home/<USER>/solana-container
