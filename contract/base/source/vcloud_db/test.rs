#[cfg(test)]
mod exec_tests {
    use super::*;
    use serde_json::json;

    fn create_test_db() -> VCloudDB {
        VCloudDB::new()
    }

    #[glue::test]
    fn test_exec_single_insert_success() {
        let mut db = create_test_db();
        
        let operations = json!([
            {
                "functionType": "insert",
                "tableName": "user_service",
                "parameters": json!({
                    "_id": "test_user_service_1",
                    "address": "test_address",
                    "provider": "test_provider",
                    "status": "active",
                    "amount": 100.0,
                    "duration": 3600,
                    "publicKey": "0x123",
                    "providerAddress": "0xprovider",
                    "serviceID": "service1",
                    "serviceActivated": true,
                    "serviceOptions": {},
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "endAt": 0,
                    "serviceActivateTS": 0,
                    "serviceRunningTS": 0,
                    "serviceAbortTS": 0,
                    "serviceDoneTS": 0,
                    "serviceRefundTS": 0,
                    "service": "test_service",
                    "createdAddr": "test_address",
                    "labelHash": "0xhash"
                }).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, Some("ok".to_string()));
        assert!(response.errors.is_empty());
        
        // Verify the record was inserted
        let get_result = db.get("user_service".to_string(), "test_user_service_1".to_string()).unwrap();
        assert!(!get_result.is_empty());
    }

    #[glue::test]
    fn test_exec_multiple_operations_success() {
        let mut db = create_test_db();

        let operations = json!([
            {
                "functionType": "insert",
                "tableName": "user_service",
                "parameters": json!({
                    "_id": "test_user_service_multi_1",
                    "address": "test_address",
                    "provider": "test_provider",
                    "status": "active",
                    "amount": 100.0,
                    "duration": 3600,
                    "publicKey": "0x123",
                    "providerAddress": "0xprovider",
                    "serviceID": "service1",
                    "serviceActivated": true,
                    "serviceOptions": {},
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "endAt": 0,
                    "serviceActivateTS": 0,
                    "serviceRunningTS": 0,
                    "serviceAbortTS": 0,
                    "serviceDoneTS": 0,
                    "serviceRefundTS": 0,
                    "service": "test_service",
                    "createdAddr": "test_address",
                    "labelHash": "0xhash"
                }).to_string()
            },
            {
                "functionType": "insert",
                "tableName": "order",
                "parameters": json!({
                    "_id": "test_order_multi_1",
                    "address": "test_address",
                    "recipient": "test_recipient",
                    "status": "pending",
                    "amount": 50.0,
                    "amountPaid": 0.0,
                    "provider": "test_provider",
                    "type": "test_order",
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "lastPaymentTS": 0,
                    "paidTS": 0,
                    "filedTS": 0,
                    "publicKey": "0x123",
                    "userServiceIDs": [],
                    "items": []
                }).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, Some("ok".to_string()));
        assert!(response.errors.is_empty());
        // Verify both records were inserted
        let user_service_result = db.get("user_service".to_string(), "test_user_service_multi_1".to_string()).unwrap();
        assert!(!user_service_result.is_empty());

        let order_result = db.get("order".to_string(), "test_order_multi_1".to_string()).unwrap();
        assert!(!order_result.is_empty());
    }

    #[glue::test]
    fn test_exec_insert_many_success() {
        let mut db = create_test_db();
        
        let operations = json!([
            {
                "functionType": "insert_many",
                "tableName": "user_service",
                "parameters": json!([
                    {
                        "_id": "test_user_service_many_1",
                        "address": "test_address_1",
                        "provider": "test_provider",
                        "status": "active",
                        "amount": 100.0,
                        "duration": 3600,
                        "publicKey": "0x123",
                        "providerAddress": "0xprovider",
                        "serviceID": "service1",
                        "serviceActivated": true,
                        "serviceOptions": {},
                        "createdAt": 0,
                        "updatedAt": 0,
                        "deletedAt": 0,
                        "endAt": 0,
                        "serviceActivateTS": 0,
                        "serviceRunningTS": 0,
                        "serviceAbortTS": 0,
                        "serviceDoneTS": 0,
                        "serviceRefundTS": 0,
                        "service": "test_service",
                        "createdAddr": "test_address_1",
                        "labelHash": "0xhash1"
                    },
                    {
                        "_id": "test_user_service_many_2",
                        "address": "test_address_2",
                        "provider": "test_provider",
                        "status": "active",
                        "amount": 200.0,
                        "duration": 3600,
                        "publicKey": "0x456",
                        "providerAddress": "0xprovider",
                        "serviceID": "service2",
                        "serviceActivated": true,
                        "serviceOptions": {},
                        "createdAt": 0,
                        "updatedAt": 0,
                        "deletedAt": 0,
                        "endAt": 0,
                        "serviceActivateTS": 0,
                        "serviceRunningTS": 0,
                        "serviceAbortTS": 0,
                        "serviceDoneTS": 0,
                        "serviceRefundTS": 0,
                        "service": "test_service",
                        "createdAddr": "test_address_2",
                        "labelHash": "0xhash2"
                    }
                ]).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, Some("ok".to_string()));
        assert!(response.errors.is_empty());
        
        // Verify both records were inserted
        let result1 = db.get("user_service".to_string(), "test_user_service_many_1".to_string()).unwrap();
        assert!(!result1.is_empty());

        let result2 = db.get("user_service".to_string(), "test_user_service_many_2".to_string()).unwrap();
        assert!(!result2.is_empty());
    }

    #[glue::test]
    fn test_exec_update_success() {
        let mut db = create_test_db();
        
        // First insert a record
        let _insert_result = db.insert("user_service".to_string(), json!({
            "_id": "test_user_service_update_1",
            "address": "test_address",
            "provider": "test_provider",
            "status": "active",
            "amount": 100.0,
            "duration": 3600,
            "publicKey": "0x123",
            "providerAddress": "0xprovider",
            "serviceID": "service1",
            "serviceActivated": true,
            "serviceOptions": {},
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "endAt": 0,
            "serviceActivateTS": 0,
            "serviceRunningTS": 0,
            "serviceAbortTS": 0,
            "serviceDoneTS": 0,
            "serviceRefundTS": 0,
            "service": "test_service",
            "createdAddr": "test_address",
            "labelHash": "0xhash"
        }).to_string()).unwrap();
        
        // Then update it via exec
        let operations = json!([
            {
                "functionType": "update",
                "tableName": "user_service",
                "parameters": json!({
                    "_id": "test_user_service_update_1",
                    "address": "test_address",
                    "provider": "test_provider",
                    "status": "updated",
                    "amount": 150.0,
                    "duration": 3600,
                    "publicKey": "0x123",
                    "providerAddress": "0xprovider",
                    "serviceID": "service1",
                    "serviceActivated": true,
                    "serviceOptions": {},
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "endAt": 0,
                    "serviceActivateTS": 0,
                    "serviceRunningTS": 0,
                    "serviceAbortTS": 0,
                    "serviceDoneTS": 0,
                    "serviceRefundTS": 0,
                    "service": "test_service",
                    "createdAddr": "test_address",
                    "labelHash": "0xhash"
                }).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, Some("ok".to_string()));
        assert!(response.errors.is_empty());
    }

    #[glue::test]
    fn test_exec_delete_success() {
        let mut db = create_test_db();
        
        // First insert a record
        let _insert_result = db.insert("user_service".to_string(), json!({
            "_id": "test_user_service_delete_1",
            "address": "test_address",
            "provider": "test_provider",
            "status": "active",
            "amount": 100.0,
            "duration": 3600,
            "publicKey": "0x123",
            "providerAddress": "0xprovider",
            "serviceID": "service1",
            "serviceActivated": true,
            "serviceOptions": {},
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "endAt": 0,
            "serviceActivateTS": 0,
            "serviceRunningTS": 0,
            "serviceAbortTS": 0,
            "serviceDoneTS": 0,
            "serviceRefundTS": 0,
            "service": "test_service",
            "createdAddr": "test_address",
            "labelHash": "0xhash"
        }).to_string()).unwrap();
        
        // Then delete it via exec
        let operations = json!([
            {
                "functionType": "delete",
                "tableName": "user_service",
                "parameters": json!({
                    "ids": ["test_user_service_delete_1"]
                }).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, Some("ok".to_string()));
        assert!(response.errors.is_empty());
    }

    #[glue::test]
    fn test_exec_first_error_stops_execution() {
        let mut db = create_test_db();

        let operations = json!([
            {
                "functionType": "insert",
                "tableName": "user_service",
                "parameters": json!({
                    "_id": "test_user_service_1",
                    "address": "test_address",
                    "provider": "test_provider",
                    "status": "active",
                    "amount": 100.0,
                    "duration": 3600,
                    "publicKey": "0x123",
                    "providerAddress": "0xprovider",
                    "serviceID": "service1",
                    "serviceActivated": true,
                    "serviceOptions": {},
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "endAt": 0,
                    "serviceActivateTS": 0,
                    "serviceRunningTS": 0,
                    "serviceAbortTS": 0,
                    "serviceDoneTS": 0,
                    "serviceRefundTS": 0,
                    "service": "test_service",
                    "createdAddr": "test_address",
                    "labelHash": "0xhash"
                }).to_string()
            },
            {
                "functionType": "insert",
                "tableName": "invalid_table", // Invalid table name - should cause error
                "parameters": json!({
                    "_id": "test_record_2"
                }).to_string()
            },
            {
                "functionType": "insert",
                "tableName": "order",
                "parameters": json!({
                    "_id": "test_order_1",
                    "address": "test_address",
                    "recipient": "test_recipient",
                    "status": "pending",
                    "amount": 50.0,
                    "amountPaid": 0.0,
                    "provider": "test_provider",
                    "type": "test_order",
                    "createdAt": 0,
                    "updatedAt": 0,
                    "deletedAt": 0,
                    "lastPaymentTS": 0,
                    "paidTS": 0,
                    "filedTS": 0,
                    "publicKey": "0x123",
                    "userServiceIDs": [],
                    "items": []
                }).to_string()
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();

        assert_eq!(response.message, None);
        assert_eq!(response.errors.len(), 1);
        assert!(response.errors[0].contains("Operation 1"));
        assert!(response.errors[0].contains("Unsupported table name"));

        // The first record should exist since it was successfully inserted before the error
        let user_service_result = db.get("user_service".to_string(), "test_user_service_1".to_string());
        assert!(user_service_result.is_ok());

        // The order should not exist because the transaction failed
        let order_result = db.get("order".to_string(), "test_order_1".to_string());
        assert!(order_result.is_err() || order_result.unwrap() == "null");
    }

    #[glue::test]
    fn test_exec_invalid_function_type() {
        let mut db = create_test_db();
        
        let operations = json!([
            {
                "functionType": "invalid_function",
                "tableName": "user_service",
                "parameters": "{}"
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, None);
        assert_eq!(response.errors.len(), 1);
        assert!(response.errors[0].contains("Unsupported function type"));
    }

    #[glue::test]
    fn test_exec_invalid_table_name() {
        let mut db = create_test_db();
        
        let operations = json!([
            {
                "functionType": "insert",
                "tableName": "invalid_table",
                "parameters": "{}"
            }
        ]);

        let result = db.exec(operations.to_string()).unwrap();
        let response: ExecResponse = serde_json::from_str(&result).unwrap();
        
        assert_eq!(response.message, None);
        assert_eq!(response.errors.len(), 1);
        assert!(response.errors[0].contains("Unsupported table name"));
    }

    #[glue::test]
    fn test_exec_invalid_json() {
        let mut db = create_test_db();
        
        let result = db.exec("invalid json".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Failed to parse operations JSON"));
    }
}
