#[cfg(test)]
mod user_service_tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_service(id: &str) -> UserService {
        UserService {
            _id: id.to_string(),
            duration: 3600,
            amount: 100.0,
            public_key: "0x123".to_string(),
            provider: "test_provider".to_string(),
            provider_address: "0xprovider".to_string(),
            address: "0xtest_addr".to_string(),
            service_id: "compute".to_string(),
            service_activated: true,
            status: "active".to_string(),
            service_options: HashMap::new(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            end_at: 0,
            service_activate_ts: 0,
            service_running_ts: 0,
            service_abort_ts: 0,
            service_done_ts: 0,
            service_refund_ts: 0,
            service: "compute_service".to_string(),
            created_addr: "0xtest_addr".to_string(),
            label_hash: "0xhash".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_user_service() {
        let mut db = VCloudDB::new();
        let service = create_test_service("test_service_1");
        let service_json = serde_json::to_string(&service).unwrap();
        
        let result = db.insert_user_service(service_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_service_1");
        
        // Verify service was inserted
        assert!(db.user_services.contains(&"test_service_1".to_string()));
    }

    #[glue::test]
    fn test_get_user_service() {
        let mut db = VCloudDB::new();
        let service = create_test_service("test_service_2");
        let service_json = serde_json::to_string(&service).unwrap();
        
        // Insert service first
        db.insert_user_service(service_json).unwrap();
        
        // Get service
        let result = db.get_user_service("test_service_2".to_string());
        assert!(result.is_ok());
        
        let retrieved_service: UserService = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_service._id, "test_service_2");
        assert_eq!(retrieved_service.provider, "test_provider");
    }

    #[glue::test]
    fn test_insert_many_user_service() {
        let mut db = VCloudDB::new();
        let services = vec![
            create_test_service("batch_1"),
            create_test_service("batch_2"),
        ];
        let services_json = serde_json::to_string(&services).unwrap();
        
        let result = db.insert_many_user_service(services_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_find_user_service() {
        let mut db = VCloudDB::new();
        let service1 = create_test_service("find_test_1");
        let mut service2 = create_test_service("find_test_2");
        service2.provider = "different_provider".to_string();
        
        // Insert services
        db.insert_user_service(serde_json::to_string(&service1).unwrap()).unwrap();
        db.insert_user_service(serde_json::to_string(&service2).unwrap()).unwrap();
        
        // Query by provider
        let query_params = UserServiceQueryParams {
            service_id: None,
            address: None,
            provider: Some("test_provider".to_string()),
            provider_address: None,
            status: None,
            service_activated: None,
            ids: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            service: None,
            statuses: None,
            start_time_from: None,
            start_time_to: None,
            end_at_from: None,
            end_at_to: None,
            label_hash: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_user_service(query_json);
        assert!(result.is_ok());
        
        let services: Vec<UserService> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(services.len(), 1);
        assert_eq!(services[0]._id, "find_test_1");
    }

    #[glue::test]
    fn test_count_user_service() {
        let mut db = VCloudDB::new();
        let service1 = create_test_service("count_test_1");
        let service2 = create_test_service("count_test_2");
        
        // Insert services
        db.insert_user_service(serde_json::to_string(&service1).unwrap()).unwrap();
        db.insert_user_service(serde_json::to_string(&service2).unwrap()).unwrap();
        
        // Count all services
        let query_params = UserServiceQueryParams {
            service_id: None,
            address: None,
            provider: None,
            provider_address: None,
            status: None,
            service_activated: None,
            ids: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            service: None,
            statuses: None,
            start_time_from: None,
            start_time_to: None,
            end_at_from: None,
            end_at_to: None,
            label_hash: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_user_service(query_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "2");
    }

    #[glue::test]
    fn test_update_user_service() {
        let mut db = VCloudDB::new();
        let mut service = create_test_service("update_test_1");
        let service_json = serde_json::to_string(&service).unwrap();
        
        // Insert service first
        db.insert_user_service(service_json).unwrap();
        
        // Update service
        service.amount = 200.0;
        service.status = "updated".to_string();
        let updated_json = serde_json::to_string(&service).unwrap();
        
        let result = db.update_user_service(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_user_service("update_test_1".to_string()).unwrap();
        let retrieved_service: UserService = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_service.amount, 200.0);
        assert_eq!(retrieved_service.status, "updated");
    }

    #[glue::test]
    fn test_delete_user_service() {
        let mut db = VCloudDB::new();
        let service = create_test_service("delete_test_1");
        let service_json = serde_json::to_string(&service).unwrap();
        
        // Insert service first
        db.insert_user_service(service_json).unwrap();
        
        // Delete service
        let filter = UserServiceQueryParams {
            ids: Some(vec!["delete_test_1".to_string()]),
            service_id: None,
            address: None,
            provider: None,
            provider_address: None,
            status: None,
            service_activated: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
            service: None,
            statuses: None,
            start_time_from: None,
            start_time_to: None,
            end_at_from: None,
            end_at_to: None,
            label_hash: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_user_service(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion - service should not be found
        let get_result = db.get_user_service("delete_test_1".to_string());
        assert!(get_result.is_err());
    }
}
