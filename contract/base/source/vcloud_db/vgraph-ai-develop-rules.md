# VGraph 项目 Cursor 开发规则

> **AI助手指导文档** - 提供VGraph项目快速代码定位和开发指导
> 
> **用途**: Cursor AI代码定位、开发任务指导、问题排查
> 
> **更新**: 2025年6月23日 | **版本**: v2.0 (优化版)

## 快速代码定位索引

### 核心功能 → 主要文件

| 功能模块 | 主要文件路径 | 核心类/函数 |
|---------|-------------|------------|
| **应用启动** | `main.py` | `VGraphApp.startApp()` |
| **链管理** | `chains/chains.py` | `CHAINS` 字典 |
| **交易处理** | `chains/transaction_processor.py` | `TransactionProcessor` |
| **状态管理** | `state/statedb.py` | `StateDB` |
| **智能合约执行** | `vm/glue.py` | `ContractExecutor` |
| **POW 共识** | `chains/vgraph_pow_primechain/chain.py` | `VGraphPOWPrimeChain` |
| **SPOS 共识** | `chains/vgraph_spos_chain/chain.py` | `VGraphSPOSChain` |
| **P2P 网络** | `jsonrpc/server.py` | `SessionManager` |
| **节点管理** | `jsonrpc/peers.py` | `PeerManager` |
| **数据库访问** | `rawdb/database.py` | `Database` |
| **缓存系统** | `cache_manager/cache_manager.py` | `CacheManager` |
| **状态缓存** | `state/statedb.py` | `CachedIndexIterator` |
| **区块链监控** | `watcher/vgraph_watcher.py` | `main()` |
| **性能监控** | `watcher/performance.py` | `PerformanceMonitor` |
| **日志检索** | `chains/log_retriever.py` | `LogRetriever` |
| **孤块管理** | `chains/orphan_block_pool.py` | `OrphanBlockPool` |

### 开发任务 → 相关文件组合

#### 智能合约开发
```
主要文件:
- 合约源码: contract/base/source/
- 测试文件: contract/base/tests/
- 执行引擎: vm/glue.py (ContractExecutor类)
- 类型定义: vm/types.py
- 工具函数: vm/utils.py

相关依赖:
- 状态访问: state/statedb.py
- 地址计算: common/address.py
- 事件日志: common/models/log.py
```

#### POW 链开发
```
主要文件:
- 链实现: chains/vgraph_pow_primechain/chain.py
- 挖矿逻辑: chains/vgraph_pow_primechain/miner.py
- 状态管理: chains/vgraph_pow_primechain/chain_state_manager.py
- 网络客户端: chains/vgraph_pow_primechain/client.py
- 会话处理: chains/vgraph_pow_primechain/session.py

相关依赖:
- 区块头管理: chains/header_chain.py
- 交易池: chains/mempool.py
- 签名验证: chains/signature_manager.py
```

#### SPOS 链开发
```
主要文件:
- 链实现: chains/vgraph_spos_chain/chain.py
- 铸币逻辑: chains/vgraph_spos_chain/minter.py
- 状态管理: chains/vgraph_spos_chain/chain_state_manager.py
- 时钟同步: chains/vgraph_spos_chain/clock.py
- 网络客户端: chains/vgraph_spos_chain/client.py

相关依赖:
- 系统合约: contract/base/spos.wasm
- 验证者管理: (内置在 minter.py)
```

#### 网络协议开发
```
主要文件:
- JSON-RPC 服务器: jsonrpc/server.py
- 会话管理基类: jsonrpc/session_base.py
- 客户端基类: jsonrpc/client_base.py
- 节点发现: jsonrpc/peer.py, jsonrpc/peers.py

相关依赖:
- 网络配置: config/config.py
- UDP 穿透: udpHolePunching/
```

#### 监控和调试
```
主要文件:
- 区块链监控: watcher/vgraph_watcher.py
- 性能监控: watcher/performance.py
- 连接管理: watcher/connection.py
- 日志聚合: watcher/log_aggregator.py

相关依赖:
- 日志检索: chains/log_retriever.py
- 配置管理: watcher/config.py
- 统计信息: watcher/stats.py
```

#### 缓存系统管理
```
主要文件:
- 缓存管理器: cache_manager/cache_manager.py
- 配置管理: cache_manager/config.py
- 缓存注册表: cache_manager/core/registry.py
- 内存监控: cache_manager/monitoring/cache_monitor.py

相关依赖:
- 大小计算: cache_manager/core/size_calculator.py
- 缓存实现: cache_manager/core/cache.py
- 使用示例: cache_manager/examples/
```

#### 性能优化
```
主要文件:
- 缓存管理: cache_manager/cache_manager.py
- 缓存实现: cache_manager/core/cache.py  
- 内存监控: cache_manager/monitoring/cache_monitor.py
- 缓存配置: cache_manager/config.py
- 状态缓存: state/statedb.py (CachedIndexIterator)
- 数据库优化: vgraphdb/lmdb.py

相关依赖:
- 状态对象: state/state_object.py
- 状态快照: state/snapshot/
- 内存池优化: chains/mempool.py
- 数据库抽象: rawdb/database.py
```

### 常用代码片段

#### 获取链实例
```python:chains/chains.py
from chains.chains import CHAINS
from config import CONFIG
chainId = CONFIG.chainId
chain_class, kwargs = CHAINS.get(chainId)
chain = chain_class() if kwargs is None else chain_class(**kwargs)
```

#### 执行智能合约
```python:vm/glue.py
from vm import ContractExecutor
executor = ContractExecutor(contract_address)
result, error = executor.execute(state, fuel, "function_name", return_type, *args)
```

#### 获取状态数据
```python:state/statedb.py
from state import StateDB
from vgraphdb import Database

# 创建状态数据库实例
database = Database("lmdb", "path_to_db")
state = StateDB(database)

# 读取和写入状态槽
value = state.getSlot(address, slot)
state.putSlot(address, slot, new_value)

# 创建快照和回滚
snapshot = state.snapshot()
# ... 执行操作 ...
state.revertToSnapshot(snapshot)
```

#### 启动区块链监控
```python:watcher/vgraph_watcher.py
from watcher.config import WatcherConfig
from watcher.connection import ConnectionManager

# 配置和启动监控
config = WatcherConfig()
conn_manager = ConnectionManager(config)
await main()  # 开始同步区块链数据
```

#### 处理交易
```python:chains/transaction_processor.py
from chains.transaction_processor import TransactionProcessor

processor = TransactionProcessor()
receipt = processor.applyTransaction(state, transaction, blockEnv, transactionIndex)
```

#### 使用缓存管理器
```python:cache_manager/cache_manager.py
from cache_manager import CacheManager, CacheManagerConfig

# 配置缓存管理器
config = CacheManagerConfig(
    totalMemoryLimit=128 * 1024 * 1024,  # 128MB
    monitorEnabled=True,
    warningThreshold=80.0,
    autoEnforce=True
)
CacheManager.configure(config)

# 创建不同类型的缓存
lru_cache = CacheManager.createCache("blocks", "lru", maxSize=1000)
size_cache = CacheManager.createCache("states", "sizeConstrained", maxSize=64*1024*1024)
ttl_cache = CacheManager.createCache("temp", "tlru", maxSize=500, ttl=300)

# 监控内存使用
usage = CacheManager.getMemoryUsagePercentage()
CacheManager.generateReport()
```

## 项目架构导航

### 系统分层结构

```
┌─────────────────────────────────────┐
│     应用层 (Application Layer)       │
│  • main.py - 启动入口               │
│  • vgraph.kv - GUI 界面             │
│  • config/ - 配置管理               │
├─────────────────────────────────────┤
│       API层 (API Layer)             │
│  • jsonrpc/server.py - RPC服务      │
│  • swaggerapi/ - 接口文档            │
│  • jsonrpc/peer.py - 网络通信        │
├─────────────────────────────────────┤
│    区块链核心层 (Blockchain Core)    │
│  • chains/chains.py - 链注册         │
│  • chains/transaction_processor.py   │
│  • chains/mempool.py - 交易池        │
│  ┌─────────────┬─────────────┐       │
│  │ POW 共识    │ SPOS 共识   │       │
│  │ vgraph_pow_ │ vgraph_spos │       │
│  │ primechain/ │ _chain/     │       │
│  └─────────────┴─────────────┘       │
├─────────────────────────────────────┤
│      执行层 (Execution Layer)        │
│  • vm/glue.py - WASM 执行引擎        │
│  • vm/contract_code.py - 合约管理     │
│  • contract/base/ - 系统合约         │
├─────────────────────────────────────┤
│      状态层 (State Layer)            │
│  • state/statedb.py - 状态数据库     │
│  • state/state_object.py - 状态对象  │
│  • cache_manager/ - 缓存管理         │
├─────────────────────────────────────┤
│     存储层 (Storage Layer)           │
│  • rawdb/database.py - 数据库抽象    │
│  • vgraphdb/lmdb.py - LMDB实现       │
│  • tree/ - Merkle 树                │
└─────────────────────────────────────┘
```

### 目录结构说明

#### 核心业务模块
- **`chains/`** - 区块链核心逻辑
  - `chains.py` - 链类型注册中心
  - `transaction_processor.py` - 交易处理引擎
  - `mempool.py` - 内存池管理
  - `header_chain.py` - 区块头链管理
  - `vgraph_pow_primechain/` - POW 共识实现
  - `vgraph_spos_chain/` - SPOS 共识实现

#### 虚拟机和合约
- **`vm/`** - WebAssembly 虚拟机
  - `glue.py` - Python-WASM 桥接层
  - `contract_code.py` - 合约代码管理
  - `types.py` - 虚拟机类型系统

- **`contract/`** - 智能合约
  - `base/` - 基础系统合约
    - `source/` - 合约源代码
    - `tests/` - 合约测试

#### 状态和存储
- **`state/`** - 状态管理
  - `statedb.py` - 状态数据库核心
  - `state_object.py` - 状态对象封装
  - `journal.py` - 状态变更日志

- **`rawdb/`** - 数据库访问层
- **`vgraphdb/`** - LMDB 数据库实现
- **`tree/`** - Merkle 树实现

#### 网络和通信
- **`jsonrpc/`** - 网络通信层
  - `server.py` - RPC 服务器
  - `peer.py` - 节点管理
  - `client_base.py` - RPC 客户端

#### 监控和调试
- **`watcher/`** - 区块链监控系统
  - `vgraph_watcher.py` - 主监控程序
  - `performance.py` - 性能监控
  - `connection.py` - 连接管理
  - `operations.py` - 操作封装

#### 工具和配置
- **`common/`** - 通用工具
  - `models/` - 数据模型
  - `address.py` - 地址工具
  - `signature.py` - 签名工具

- **`config/`** - 配置文件
- **`scripts/`** - 辅助脚本

#### 缓存管理系统
- **`cache_manager/`** - 完整的缓存管理系统
  - `cache_manager.py` - 缓存管理器主类，单例模式
  - `config.py` - 缓存配置管理
  - `core/cache.py` - 缓存实现（LRU、TLRU、SizeConstrained）
  - `core/registry.py` - 缓存注册表和内存限制
  - `monitoring/cache_monitor.py` - 实时内存使用监控
  - `examples/` - 使用示例和最佳实践

## 开发任务指南

### 常见开发场景

#### 1. 添加新的合约功能
**步骤**:
1. 在 `contract/base/source/` 修改合约源码
2. 在 `contract/base/tests/` 添加测试
3. 重新编译: `cd contract/base && make`
4. 更新相关的 Python 调用代码

**相关文件**:
- `vm/glue.py` - 合约执行引擎
- `vm/types.py` - 类型定义
- `chains/transaction_processor.py` - 交易处理

#### 2. 修改共识算法
**POW 相关修改**:
- `chains/vgraph_pow_primechain/miner.py` - 挖矿逻辑
- `chains/vgraph_pow_primechain/chain_state_manager.py` - 状态管理

**SPOS 相关修改**:
- `chains/vgraph_spos_chain/minter.py` - 铸币逻辑
- `chains/vgraph_spos_chain/clock.py` - 时间同步

#### 3. 网络协议更新
**主要文件**:
- `jsonrpc/server.py` - 服务器实现
- `chains/{chain_type}/session.py` - 会话处理
- `chains/{chain_type}/client.py` - 客户端实现

#### 4. 性能优化
**缓存优化**:
- `cache_manager/cache_manager.py` - 缓存策略和管理
- `cache_manager/core/cache.py` - LRU、TLRU、大小限制缓存
- `state/statedb.py` - 状态缓存和索引迭代器

**数据库优化**:
- `vgraphdb/lmdb.py` - 数据库访问
- `rawdb/` - 数据库抽象层

**内存管理**:
- `chains/mempool.py` - 内存池优化
- `cache_manager/monitoring/cache_monitor.py` - 缓存内存监控

#### 5. 缓存系统开发
**缓存管理**:
- `cache_manager/cache_manager.py` - 主缓存管理器
- `cache_manager/core/cache.py` - 缓存算法实现
- `cache_manager/core/registry.py` - 缓存注册和限制

**监控和配置**:
- `cache_manager/monitoring/cache_monitor.py` - 实时监控
- `cache_manager/config.py` - 配置管理

### 开发工具和命令

#### 测试命令
```bash
# 运行所有测试
poetry run pytest

# 运行特定模块测试
poetry run pytest chains/tests/
poetry run pytest vm/tests/
poetry run pytest contract/base/tests/

# 合约测试
cd contract/base && make test
```

#### 合约开发
```bash
# 编译合约
cd contract/base && make

# 获取合约地址
python scripts/get_contract_address.py

# 获取合约字节码
python scripts/get_contract_hex_bytecode.py
```

#### 代码质量
```bash
# 代码格式化
poetry run black .
poetry run isort .

# 类型检查
poetry run mypy .

# 代码检查
poetry run flake8 .
```

## 代码规范

### Python 代码规范
- 使用 snake_case 命名文件
- 使用 camelCase 命名函数和变量
- 避免缩写变量名
- Python 版本要求: '>=3.11, <=3.12'

### 文件命名约定
- 模块文件: `module_name.py`
- 测试文件: `test_module_name.py`
- 配置文件: `config.py` 或 `config.yaml`

### 导入规范
```python
# 标准库导入
import os
from typing import Dict, List

# 第三方库导入
from kivy.logger import Logger

# 本地导入
from common import bytesToHex
from state import StateDB
```

## 调试和故障排除

### 常见问题定位

#### 合约执行错误
**查看文件**: `vm/glue.py` - `ContractExecutor` 类
**日志位置**: `logs/` 目录
**调试方法**: 启用 WASM 执行日志

#### 网络连接问题
**查看文件**: `jsonrpc/peer.py`, `jsonrpc/server.py`
**配置文件**: `config/config.yaml`
**调试工具**: UDP 穿透测试工具在 `udpHolePunching/`

#### 状态同步问题
**查看文件**: `chains/{chain_type}/chain_state_manager.py`
**数据库工具**: 检查 `vgraphdb/` 相关文件

#### 性能问题
**监控工具**: `cache_manager/monitoring/cache_monitor.py`
**分析文件**: 
- `cache_manager/cache_manager.py` - 缓存策略分析和内存使用
- `state/statedb.py` - 状态缓存性能分析
- `chains/mempool.py` - 内存池效率检查
- `vgraphdb/lmdb.py` - 数据库读写性能

### 模块依赖关系

#### 核心依赖链
```
main.py
  ├── chains/chains.py (链选择)
  ├── config/config.py (配置加载)
  └── jsonrpc/server.py (网络服务)
      └── chains/{chain_type}/session.py (协议处理)

chains/{chain_type}/chain.py
  ├── chains/transaction_processor.py (交易处理)
  ├── state/statedb.py (状态管理)
  ├── chains/mempool.py (内存池)
  └── vm/glue.py (合约执行)

vm/glue.py
  ├── state/statedb.py (状态读写)
  ├── contract/base/*.wasm (合约代码)
  └── common/models/ (数据结构)
```

#### 数据流依赖
- **配置**: `config/config.py` → 所有模块
- **状态**: `state/statedb.py` ← → `rawdb/database.py` ← → `vgraphdb/lmdb.py`
- **网络**: `jsonrpc/` ← → `chains/{chain_type}/session.py`
- **监控**: `watcher/` → `jsonrpc/client_base.py` → `chains/`

### 测试策略

#### 单元测试
- 每个模块都有对应的测试文件
- 使用 pytest 框架
- 测试文件位于各模块的 `tests/` 目录

#### 集成测试
- 完整的区块链功能测试
- 合约部署和执行测试
- 网络通信测试

#### 合约测试
- Rust 合约单元测试
- Python 调用测试
- 性能基准测试

## 外部工具集成

### VGraph 生态工具

#### vgraph-sdk (Rust SDK)
- 位置: `../vgraph-sdk/`
- 功能: RPC 客户端、钱包、交易构建
- 文档: `vgraph-sdk/docs/`

#### cargo-vcontract (合约开发工具)
- 位置: `../cargo-vcontract/`
- 命令: `deploy`, `call`, `query`, `upgrade`, `fork`
- 配置: 参考 `.cursor/rules/cargo-vcontract.mdc`

#### py-vgraph (Python SDK)
- 位置: `../py-vgraph/`
- 功能: Python 绑定、钱包功能
- 示例: `py-vgraph/examples/`

---

*本规则文件针对 Cursor AI 助手优化，提供快速代码定位和开发指导。最后更新：2025年6月23日* 