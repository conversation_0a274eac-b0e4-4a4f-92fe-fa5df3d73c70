# VCloud DB 合约功能文档

## 概述

VCloud DB 是一个基于区块链的分布式数据库合约，提供统一的数据存储和管理接口。合约支持多种数据表类型，每种表都有完整的CRUD操作支持。

## 核心架构

### 统一接口设计

合约采用统一接口设计，所有数据表都通过以下10个标准函数进行操作：

1. **get(table_name, id)** - 根据ID获取单条记录
2. **find(table_name, filter_json_string)** - 根据条件查询多条记录
3. **count(table_name, filter_json_string)** - 统计符合条件的记录数量
4. **insert(table_name, insert_json_string)** - 插入单条记录
5. **insert_many(table_name, insert_many_json_string)** - 批量插入多条记录
6. **update(table_name, update_json_string)** - 更新单条记录（完整替换）
7. **update_many(table_name, update_many_json_string)** - 批量更新多条记录（部分字段更新）
8. **bulk_write(table_name, bulk_write_json_string)** - 批量写入操作（支持混合操作）
9. **delete(table_name, filter_json_string)** - 删除单条记录
10. **delete_many(table_name, filter_json_string)** - 批量删除多条记录

### 支持的数据表

目前合约支持以下数据表：

1. **user_service** - 用户服务表
2. **order** - 订单表
3. **order_service** - 订单服务关联表
4. **cli_version** - CLI版本表
5. **currency** - 货币表
6. **service_category** - 服务分类表
7. **provider** - 服务提供商表
8. **service_type** - 服务类型表（新增）

## 数据表详细说明

### 1. UserService（用户服务表）

**字段结构：**
```json
{
  "_id": "string",
  "createdAt": "int64",
  "updatedAt": "int64", 
  "deletedAt": "int64",
  "duration": "int64",
  "endAt": "int64",
  "status": "string",
  "serviceActivated": "bool",
  "serviceActivateTS": "int64",
  "serviceRunningTS": "int64",
  "serviceAbortTS": "int64",
  "serviceDoneTS": "int64",
  "serviceRefundTS": "int64",
  "serviceID": "string",
  "service": "string",
  "address": "string",
  "createdAddr": "string",
  "provider": "string",
  "providerAddress": "string",
  "serviceOptions": "map[string]string",
  "amount": "float64"
}
```

**索引：**
- address + created_at
- provider + created_at
- status + created_at
- created_at
- updated_at

### 2. Order（订单表）

**字段结构：**
```json
{
  "_id": "string",
  "createdAt": "int64",
  "updatedAt": "int64",
  "deletedAt": "int64",
  "userServiceID": "string",
  "orderStatus": "string",
  "orderType": "string",
  "amount": "float64",
  "currency": "string",
  "paymentMethod": "string",
  "transactionHash": "string",
  "userAddress": "string",
  "providerAddress": "string"
}
```

**索引：**
- userServiceID + created_at
- userAddress + created_at
- orderStatus + created_at
- orderType + created_at
- created_at

### 3. ServiceType（服务类型表）

**字段结构：**
```json
{
  "_id": "string",
  "createdAt": "int64",
  "updatedAt": "int64",
  "deletedAt": "int64",
  "name": "string",
  "provider": "string",
  "refundable": "bool",
  "categoryID": "string",
  "category": "string",
  "serviceOptions": "map[string][]string",
  "description": "string",
  "apiHost": "string",
  "durationToPrice": "[]PriceSet",
  "serviceOptionDesc": "map[string]map[string]string"
}
```

**PriceSet子结构：**
```json
{
  "price": "float64",
  "chargingOptions": "map[string]string",
  "duration": "map[int64]float64"
}
```

**索引：**
- provider + created_at
- category + created_at
- name + created_at
- created_at
- updated_at

### 4. Provider（服务提供商表）

**字段结构：**
```json
{
  "_id": "string",
  "createdAt": "int64",
  "updatedAt": "int64",
  "deletedAt": "int64",
  "name": "string",
  "walletAddress": "string",
  "publickey": "string",
  "category2ID": "map[string]string",
  "signAddress": "string",
  "apiHost": "string"
}
```

**索引：**
- name + created_at
- created_at

### 5. ServiceCategory（服务分类表）

**字段结构：**
```json
{
  "_id": "string",
  "createdAt": "int64",
  "updatedAt": "int64",
  "deletedAt": "int64",
  "provider": "string",
  "name": "string",
  "serviceOptions": "map[string][]string",
  "description": "string",
  "name2ID": "map[string]string",
  "apiHost": "string"
}
```

**索引：**
- provider + created_at
- name + created_at
- created_at

## 核心功能特性

### 1. 时间戳自动管理
- **createdAt**: 创建时自动设置为当前时间戳（如果输入为0）
- **updatedAt**: 更新时自动设置为当前时间戳（如果输入为0）
- **deletedAt**: 软删除标记，0表示未删除

### 2. 高效索引系统
- 每个表都有多个复合索引，支持高效查询
- 索引格式：`字段值-时间戳`，支持时间范围查询和排序

### 3. 灵活的查询系统
- 支持多字段过滤
- 支持分页（offset/limit）
- 支持排序（sortBy/sortDesc）
- 支持时间范围查询

### 4. 批量操作支持
- **insert_many**: 批量插入，支持部分失败处理
- **update_many**: 批量更新，支持部分字段更新
- **delete_many**: 批量删除
- **bulk_write**: 混合批量操作（插入、更新、删除）

### 5. 错误处理机制
- 详细的错误信息返回
- 批量操作中的错误收集
- 数据验证和约束检查

## 使用示例

### 插入服务类型
```python
service_type = {
    "_id": "st_001",
    "name": "云服务器",
    "provider": "阿里云",
    "category": "计算",
    "refundable": True,
    "description": "高性能云服务器",
    "serviceOptions": {
        "cpu": ["1核", "2核", "4核"],
        "memory": ["1GB", "2GB", "4GB"]
    }
}
result, err = client.execute("insert", str, "service_type", json.dumps(service_type))
```

### 查询服务类型
```python
filter_params = {
    "provider": "阿里云",
    "category": "计算",
    "limit": 10,
    "offset": 0
}
result, err = client.executeReadOnly("find", str, "service_type", json.dumps(filter_params))
```

### 批量更新
```python
update_params = {
    "filter": {"provider": "阿里云"},
    "update_data": {"description": "更新后的描述"}
}
result, err = client.execute("update_many", str, "service_type", json.dumps(update_params))
```

## 返回格式

### 单条记录操作
- **get**: 返回JSON格式的记录数据
- **insert**: 返回插入记录的ID
- **update**: 返回空JSON对象 `{}`
- **delete**: 返回 `{"deleted": 1}` 或 `{"deleted": 0}`

### 批量操作
```json
{
  "created": 2,
  "updated": 1,
  "deleted": 0,
  "errors": []
}
```

### 查询操作
- **find**: 返回记录数组的JSON
- **count**: 返回数字字符串

## 最佳实践

1. **使用合适的索引**: 根据查询条件选择最优的过滤字段
2. **批量操作**: 对于大量数据操作，优先使用批量函数
3. **错误处理**: 始终检查返回的错误信息
4. **分页查询**: 对于大结果集，使用limit和offset进行分页
5. **时间戳管理**: 让合约自动管理时间戳（传入0值）

## 技术规范

- **语言**: Rust
- **框架**: Glue Framework
- **存储**: 区块链分布式存储
- **索引**: 复合索引，支持范围查询
- **序列化**: JSON格式
- **错误处理**: Result<T, anyhow::Error>模式

## API接口详细说明

### 查询参数结构

每个表都有对应的QueryParams结构，支持以下通用字段：

```json
{
  "ids": ["id1", "id2"],           // 批量ID查询
  "createdAtStart": 1640995200,    // 创建时间范围开始
  "createdAtEnd": 1672531200,      // 创建时间范围结束
  "updatedAtStart": 1640995200,    // 更新时间范围开始
  "updatedAtEnd": 1672531200,      // 更新时间范围结束
  "offset": 0,                     // 分页偏移量
  "limit": 100,                    // 分页大小
  "sortBy": "createdAt",           // 排序字段
  "sortDesc": false                // 是否降序排列
}
```

### 更新参数结构

```json
{
  "filter": {
    // 查询条件，使用对应表的QueryParams格式
  },
  "update_data": {
    // 要更新的字段，只需包含需要更新的字段
  }
}
```

### 批量写入操作结构

```json
[
  {
    "type": "insert",
    "data": {
      // 要插入的记录数据
    }
  },
  {
    "type": "update",
    "filter": {
      // 更新条件
    },
    "data": {
      // 更新数据
    }
  },
  {
    "type": "delete_many",
    "filter": {
      // 删除条件
    }
  }
]
```

## 性能优化建议

### 1. 索引使用策略
- 优先使用有索引的字段进行查询
- 复合索引按照 `字段值-时间戳` 格式，支持前缀匹配
- 时间范围查询会自动使用时间索引

### 2. 查询优化
- 使用具体的字段值而不是范围查询（当可能时）
- 合理设置limit避免返回过多数据
- 利用offset进行分页而不是一次性加载所有数据

### 3. 批量操作优化
- 大量数据操作时使用批量函数
- bulk_write可以在单次调用中执行多种操作
- 批量操作具有更好的性能和原子性

## 数据一致性保证

### 1. 原子性
- 单条记录操作具有原子性
- 批量操作在单个事务中执行

### 2. 数据验证
- 必填字段验证（如_id不能为空）
- 数据类型验证
- 业务逻辑验证

### 3. 软删除机制
- 使用deletedAt字段实现软删除
- 查询时自动过滤已删除记录
- 支持数据恢复（通过将deletedAt设为0）

## 监控和调试

### 1. 错误日志
- 详细的错误信息包含操作类型和失败原因
- 批量操作中的部分失败会被收集到errors数组中

### 2. 性能监控
- 可以通过count操作监控数据量
- 查询性能可以通过limit测试评估

### 3. 数据完整性检查
- 定期使用count验证数据一致性
- 通过时间范围查询检查数据分布

## 扩展性设计

### 1. 新表添加
- 遵循现有模式添加新的数据表
- 实现对应的结构体、查询参数、索引定义
- 在统一接口中添加路由

### 2. 字段扩展
- 新增字段时保持向后兼容
- 使用Option类型处理可选字段
- 更新对应的查询和更新逻辑

### 3. 索引优化
- 根据查询模式添加新的复合索引
- 定期评估索引使用效率
- 移除不必要的索引以节省存储空间

## 安全考虑

### 1. 输入验证
- 所有JSON输入都经过严格解析和验证
- 防止SQL注入等安全问题
- 字段长度和格式验证

### 2. 权限控制
- 通过合约级别的权限控制访问
- 支持只读和读写操作分离

### 3. 数据隐私
- 敏感数据可以通过加密存储
- 支持字段级别的访问控制

## 版本兼容性

- 当前版本支持所有定义的数据表和操作
- 向后兼容现有的API接口
- 新功能通过可选参数添加，不影响现有功能

## 总结

VCloud DB合约提供了一个完整、高效、可扩展的区块链数据库解决方案。通过统一的接口设计、灵活的查询系统、高效的索引机制和完善的错误处理，为分布式应用提供了可靠的数据存储和管理能力。
