#[cfg(test)]
mod order_tests {
    use super::*;

    fn create_test_order(id: &str) -> Order {
        Order {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            order_type: "compute_order".to_string(),
            amount: 500.0,
            amount_paid: 0.0,
            provider: "test_provider".to_string(),
            address: "0xtest_address".to_string(),
            recipient: "0xrecipient".to_string(),
            status: "pending".to_string(),
            last_payment_ts: 0,
            paid_ts: 0,
            filed_ts: 0,
            public_key: "0x123".to_string(),
            user_service_ids: vec!["service_1".to_string(), "service_2".to_string()],
            items: vec![
                SingleServiceBriefInfo {
                    user_service_id: "service_1".to_string(),
                    duration: 3600,
                    amount: 250.0,
                },
                SingleServiceBriefInfo {
                    user_service_id: "service_2".to_string(),
                    duration: 7200,
                    amount: 250.0,
                },
            ],
        }
    }

    #[glue::test]
    fn test_insert_order() {
        let mut db = VCloudDB::new();
        let order = create_test_order("test_order_1");
        let order_json = serde_json::to_string(&order).unwrap();
        
        let result = db.insert_order(order_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_order_1");
        
        // Verify order was inserted
        assert!(db.orders.contains(&"test_order_1".to_string()));
    }

    #[glue::test]
    fn test_get_order() {
        let mut db = VCloudDB::new();
        let order = create_test_order("test_order_2");
        let order_json = serde_json::to_string(&order).unwrap();
        
        // Insert order first
        db.insert_order(order_json).unwrap();
        
        // Get order
        let result = db.get_order("test_order_2".to_string());
        assert!(result.is_ok());
        
        let retrieved_order: Order = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_order._id, "test_order_2");
        assert_eq!(retrieved_order.provider, "test_provider");
    }

    #[glue::test]
    fn test_insert_many_order() {
        let mut db = VCloudDB::new();
        let orders = vec![
            create_test_order("batch_order_1"),
            create_test_order("batch_order_2"),
        ];
        let orders_json = serde_json::to_string(&orders).unwrap();
        
        let result = db.insert_many_order(orders_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_find_order() {
        let mut db = VCloudDB::new();
        let order1 = create_test_order("find_order_1");
        let mut order2 = create_test_order("find_order_2");
        order2.provider = "different_provider".to_string();
        
        // Insert orders
        db.insert_order(serde_json::to_string(&order1).unwrap()).unwrap();
        db.insert_order(serde_json::to_string(&order2).unwrap()).unwrap();
        
        // Query by provider (using service field for provider matching)
        let query_params = OrderQueryParam {
            ids: None,
            service_id: None,
            service: Some("test_provider".to_string()),
            address: None,
            recipient: None,
            order_type: None,
            statuses: None,
            ts_start: None,
            ts_end: None,
            limit: None,
            offset: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_order(query_json);
        assert!(result.is_ok());
        
        let orders: Vec<Order> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(orders.len(), 1);
        assert_eq!(orders[0]._id, "find_order_1");
    }

    #[glue::test]
    fn test_count_order() {
        let mut db = VCloudDB::new();
        let order1 = create_test_order("count_order_1");
        let order2 = create_test_order("count_order_2");
        
        // Insert orders
        db.insert_order(serde_json::to_string(&order1).unwrap()).unwrap();
        db.insert_order(serde_json::to_string(&order2).unwrap()).unwrap();
        
        // Count all orders
        let query_params = OrderQueryParam {
            ids: None,
            service_id: None,
            service: None,
            address: None,
            recipient: None,
            order_type: None,
            statuses: None,
            ts_start: None,
            ts_end: None,
            limit: None,
            offset: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_order(query_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "2");
    }

    #[glue::test]
    fn test_update_order() {
        let mut db = VCloudDB::new();
        let mut order = create_test_order("update_order_1");
        let order_json = serde_json::to_string(&order).unwrap();
        
        // Insert order first
        db.insert_order(order_json).unwrap();
        
        // Update order
        order.amount = 1000.0;
        order.status = "paid".to_string();
        let updated_json = serde_json::to_string(&order).unwrap();
        
        let result = db.update_order(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_order("update_order_1".to_string()).unwrap();
        let retrieved_order: Order = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_order.amount, 1000.0);
        assert_eq!(retrieved_order.status, "paid");
    }

    #[glue::test]
    fn test_delete_order() {
        let mut db = VCloudDB::new();
        let order = create_test_order("delete_order_1");
        let order_json = serde_json::to_string(&order).unwrap();
        
        // Insert order first
        db.insert_order(order_json).unwrap();
        
        // Delete order
        let filter = OrderQueryParam {
            ids: Some(vec!["delete_order_1".to_string()]),
            service_id: None,
            service: None,
            address: None,
            recipient: None,
            order_type: None,
            statuses: None,
            ts_start: None,
            ts_end: None,
            limit: None,
            offset: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_order(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion - order should not be found
        let get_result = db.get_order("delete_order_1".to_string());
        assert!(get_result.is_err());
    }

    #[glue::test]
    fn test_bulk_write_order() {
        let mut db = VCloudDB::new();
        
        // Create bulk write operations
        let operations = vec![
            OrderBulkWriteOperation {
                operation_type: "insert".to_string(),
                data: Some(serde_json::to_value(create_test_order("bulk_1")).unwrap()),
                filter: None,
            },
            OrderBulkWriteOperation {
                operation_type: "insert".to_string(),
                data: Some(serde_json::to_value(create_test_order("bulk_2")).unwrap()),
                filter: None,
            },
        ];
        let operations_json = serde_json::to_string(&operations).unwrap();
        
        let result = db.bulk_write_order(operations_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify orders were created
        assert!(db.orders.contains(&"bulk_1".to_string()));
        assert!(db.orders.contains(&"bulk_2".to_string()));
    }
}
