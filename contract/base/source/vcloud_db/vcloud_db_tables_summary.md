# VCloud DB 数据表结构与查询方式总结

## 概述

VCloud DB 是一个基于区块链的分布式数据库合约，提供统一的数据存储和管理接口。合约采用统一接口设计，支持8种数据表类型，每种表都有完整的CRUD操作支持。

## 统一接口函数

合约提供10个标准统一接口函数，通过 `table_name` 参数路由到具体的表操作：

### 只读函数
1. **get(table_name, id)** - 根据ID获取单条记录
2. **find(table_name, filter_json_string)** - 根据条件查询多条记录  
3. **count(table_name, filter_json_string)** - 统计符合条件的记录数量

### 写入函数
4. **insert(table_name, insert_json_string)** - 插入单条记录
5. **insert_many(table_name, insert_many_json_string)** - 批量插入多条记录
6. **update(table_name, update_json_string)** - 更新单条记录（完整替换）
7. **update_many(table_name, update_many_json_string)** - 批量更新多条记录（部分字段更新）
8. **bulk_write(table_name, bulk_write_json_string)** - 批量写入操作（支持混合操作）
9. **delete(table_name, filter_json_string)** - 删除单条记录
10. **delete_many(table_name, filter_json_string)** - 批量删除多条记录

## 支持的数据表

| 表名 | table_name | 描述 |
|------|------------|------|
| UserService | user_service | 用户服务表 |
| Order | order | 订单表 |
| OrderService | order_service | 订单服务关联表 |
| CliVersion | cli_version | CLI版本表 |
| Currency | currency | 货币表 |
| ServiceCategory | service_category | 服务分类表 |
| Provider | provider | 服务提供商表 |
| ServiceType | service_type | 服务类型表 |

## 数据表详细结构

### 1. UserService（用户服务表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "duration": "int64",                // 服务持续时间
  "amount": "float64",                // 服务金额
  "publicKey": "string",              // 公钥
  "provider": "string",               // 服务提供商
  "providerAddress": "string",        // 提供商地址
  "address": "string",                // 用户地址
  "serviceID": "string",              // 服务ID
  "serviceActivated": "bool",         // 服务是否激活
  "status": "string",                 // 服务状态
  "serviceOptions": "map[string]string", // 服务选项
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "endAt": "int64",                   // 结束时间
  "serviceActivateTS": "int64",       // 服务激活时间戳
  "serviceRunningTS": "int64",        // 服务运行时间戳
  "serviceAbortTS": "int64",          // 服务中止时间戳
  "serviceDoneTS": "int64",           // 服务完成时间戳
  "serviceRefundTS": "int64",         // 服务退款时间戳
  "service": "string",                // 服务名称
  "createdAddr": "string",            // 创建地址
  "labelHash": "string"               // 标签哈希
}
```

**索引：**
- `user_services_provider` - provider + created_at
- `user_services_address` - address + created_at
- `user_services_provider_address` - provider_address + created_at
- `user_services_status` - status + created_at
- `user_services_service_id` - service_id + created_at
- `user_services_service_activated` - service_activated + created_at
- `user_services_address_status` - address + status + created_at（复合索引）
- `user_services_provider_status` - provider + status + created_at（复合索引）
- `user_services_address_service_activated` - address + service_activated + created_at（复合索引）
- `user_services_created_at` - created_at
- `user_services_updated_at` - updated_at

### 2. Order（订单表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "type": "string",                   // 订单类型
  "amount": "float64",                // 订单金额
  "amountPaid": "float64",            // 已支付金额
  "provider": "string",               // 服务提供商
  "address": "string",                // 用户地址
  "recipient": "string",              // 收件人地址
  "status": "string",                 // 订单状态
  "lastPaymentTS": "int64",           // 最后支付时间戳
  "paidTS": "int64",                  // 支付时间戳
  "filedTS": "int64",                 // 提交时间戳
  "publicKey": "string",              // 公钥
  "userServiceIDs": "[]string",       // 用户服务ID列表
  "items": "[]SingleServiceBriefInfo" // 订单项目列表
}
```

**索引：**
- `order_address` - address + created_at
- `order_recipient` - recipient + created_at
- `order_provider` - provider + created_at
- `order_status` - status + created_at
- `order_type` - order_type + created_at
- `order_paid_ts` - paid_ts
- `order_filed_ts` - filed_ts
- `order_created_at` - created_at
- `order_address_status` - address + status + created_at（复合索引）

### 3. OrderService（订单服务关联表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "orderID": "string",                // 订单ID
  "userServiceID": "string",          // 用户服务ID
  "orderStatus": "string",            // 订单状态
  "orderType": "string"               // 订单类型
}
```

**索引：**
- `order_services_user_service_id` - user_service_id + created_at
- `order_services_order_id` - order_id + created_at
- `order_services_order_type` - order_type + created_at
- `order_services_order_status` - order_status + created_at
- `order_services_created_at` - created_at

### 4. CliVersion（CLI版本表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "version": "string",                // 版本号
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "changeLog": "string",              // 变更日志
  "minimalSupported": "string"        // 最小支持版本
}
```

**索引：**
- `cli_versions_created_at` - created_at
- `cli_versions_version` - version + created_at

### 5. Currency（货币表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "nameOrId": "string",               // 名称或ID
  "contractId": "string",             // 合约ID
  "symbolName": "string",             // 符号名称
  "contractType": "string",           // 合约类型
  "unit": "int32",                    // 单位
  "exchangeRate": "float64"           // 汇率
}
```

**索引：**
- `currencies_created_at` - created_at
- `currencies_name_or_id` - name_or_id + created_at
- `currencies_symbol_name` - symbol_name + created_at
- `currencies_contract_id` - contract_id + created_at

### 6. ServiceCategory（服务分类表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "provider": "string",               // 服务提供商
  "name": "string",                   // 分类名称
  "serviceOptions": "map[string][]string", // 服务选项
  "description": "string",            // 描述
  "name2ID": "map[string]string",     // 名称到ID映射
  "apiHost": "string"                 // API主机
}
```

**索引：**
- `service_categories_created_at` - created_at
- `service_categories_provider` - provider + created_at
- `service_categories_name` - name + created_at

### 7. Provider（服务提供商表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "name": "string",                   // 提供商名称
  "walletAddress": "string",          // 钱包地址
  "publickey": "string",              // 公钥
  "category2ID": "map[string]string", // 分类到ID映射
  "signAddress": "string",            // 签名地址
  "apiHost": "string"                 // API主机
}
```

**索引：**
- `providers_created_at` - created_at
- `providers_name` - name + created_at

### 8. ServiceType（服务类型表）

**核心字段：**
```json
{
  "_id": "string",                    // 主键ID
  "createdAt": "int64",               // 创建时间
  "updatedAt": "int64",               // 更新时间
  "deletedAt": "int64",               // 删除时间
  "name": "string",                   // 服务类型名称
  "provider": "string",               // 服务提供商
  "refundable": "bool",               // 是否可退款
  "categoryID": "string",             // 分类ID
  "category": "string",               // 分类名称
  "serviceOptions": "map[string][]string", // 服务选项
  "description": "string",            // 描述
  "apiHost": "string",                // API主机
  "durationToPrice": "[]PriceSet",    // 持续时间到价格映射
  "serviceOptionDesc": "map[string]map[string]string" // 服务选项描述
}
```

**索引：**
- `service_types_provider` - provider + created_at
- `service_types_category` - category + created_at
- `service_types_name` - name + created_at
- `service_types_created_at` - created_at
- `service_types_updated_at` - updated_at

## 查询参数结构

### 通用查询参数

所有表的QueryParams都支持以下通用字段：

```json
{
  "ids": ["id1", "id2"],              // 批量ID查询（优先级最高）
  "createdAtStart": **********,       // 创建时间范围开始
  "createdAtEnd": **********,         // 创建时间范围结束
  "updatedAtStart": **********,       // 更新时间范围开始
  "updatedAtEnd": **********,         // 更新时间范围结束
  "offset": 0,                        // 分页偏移量
  "limit": 100,                       // 分页大小
  "sortBy": "createdAt",              // 排序字段（部分表支持）
  "sortDesc": false                   // 是否降序排列
}
```

### 表特定查询参数

#### UserServiceQueryParams
```json
{
  "serviceID": "string",              // 服务ID过滤
  "address": "string",                // 用户地址过滤
  "provider": "string",               // 提供商过滤
  "providerAddress": "string",        // 提供商地址过滤
  "status": "string",                 // 状态过滤
  "serviceActivated": true/false      // 服务激活状态过滤
}
```

#### OrderQueryParam
```json
{
  "serviceID": "string",              // 服务ID过滤
  "service": "string",                // 服务名称过滤
  "address": "string",                // 用户地址过滤
  "recipient": "string",              // 收件人过滤
  "type": "string",                   // 订单类型过滤
  "statuses": ["status1", "status2"], // 多状态过滤
  "tsStart": **********,              // 时间戳范围开始
  "tsEnd": **********                 // 时间戳范围结束
}
```

#### CliVersionQueryParams
```json
{
  "version": "string",                // 版本号过滤
  "minimalSupported": "string"        // 最小支持版本过滤
}
```

#### CurrencyQueryParams
```json
{
  "nameOrId": "string",               // 名称或ID过滤
  "contractId": "string",             // 合约ID过滤
  "symbolName": "string",             // 符号名称过滤
  "contractType": "string"            // 合约类型过滤
}
```

#### ServiceCategoryQueryParams
```json
{
  "provider": "string",               // 提供商过滤
  "name": "string"                    // 分类名称过滤
}
```

#### ProviderQueryParams
```json
{
  "name": "string",                   // 提供商名称过滤
  "walletAddress": "string",          // 钱包地址过滤
  "publickey": "string",              // 公钥过滤
  "signAddress": "string",            // 签名地址过滤
  "apiHost": "string"                 // API主机过滤
}
```

#### OrderServiceQueryParams
```json
{
  "orderID": "string",                // 订单ID过滤
  "userServiceID": "string",          // 用户服务ID过滤
  "orderStatus": "string",            // 订单状态过滤
  "orderType": "string"               // 订单类型过滤
}
```

#### ServiceTypeQueryParams
```json
{
  "name": "string",                   // 服务类型名称过滤
  "provider": "string",               // 提供商过滤
  "category": "string",               // 分类过滤
  "categoryID": "string",             // 分类ID过滤
  "refundable": true/false            // 是否可退款过滤
}
```

## 查询优化策略

### 索引选择优先级

系统根据查询参数自动选择最优索引，优先级如下：

1. **批量ID查询** - 直接通过主键查找（最高优先级）
2. **复合索引** - 当多个过滤条件匹配复合索引时使用
3. **单字段索引** - 根据单个过滤条件选择对应索引
4. **时间索引** - 默认使用created_at索引进行全表扫描

### 复合索引使用示例

#### UserService表复合索引
- `address + status` → 使用 `user_services_address_status` 索引
- `provider + status` → 使用 `user_services_provider_status` 索引
- `address + serviceActivated` → 使用 `user_services_address_service_activated` 索引

#### Order表复合索引
- `address + statuses` → 使用 `order_address_status` 索引

### 查询性能优化

1. **索引覆盖查询** - 所有索引都包含created_at字段，支持高效排序
2. **前缀匹配** - 复合索引支持前缀匹配查询
3. **范围查询** - 时间戳字段支持范围查询优化
4. **批量查询** - ID批量查询通过主键直接访问，性能最优

## 分页和排序机制

### 分页参数

```json
{
  "limit": 100,                       // 每页记录数，默认无限制
  "offset": 0                         // 跳过的记录数，默认0
}
```

### 排序参数

```json
{
  "sortDesc": false                   // 排序方向，false=升序，true=降序
}
```

**注意：** 大部分表不支持自定义sortBy字段，排序主要基于索引中的created_at字段。

### 排序实现机制

#### 1. 索引级排序
- 所有索引都以 `{field}-{created_at}` 格式构建
- created_at使用19位零填充格式确保字典序正确
- 支持正向和反向迭代实现升序/降序

#### 2. 迭代器排序
```rust
// 升序：从最小时间戳开始
let (start_key, end_key, reverse) = (
    format!("{:0>19}", 0),           // 从0开始
    format!("{:9>19}", i64::MAX),    // 到最大值
    false                            // 正向迭代
);

// 降序：从最大时间戳开始
let (start_key, end_key, reverse) = (
    format!("{:9>19}", i64::MAX),    // 从最大值开始
    format!("{:0>19}", 0),           // 到0
    true                             // 反向迭代
);
```

#### 3. 复合索引排序
对于复合索引，排序键格式为：`{field1}-{field2}-{created_at}`

```rust
// 示例：address + status + created_at
let key_prefix = format!("{}-{}", address, status);
let (start_key, end_key, reverse) = if sort_desc {
    (format!("{}-{:9>19}", key_prefix, i64::MAX),
     format!("{}-{:0>19}", key_prefix, 0), true)
} else {
    (format!("{}-{:0>19}", key_prefix, 0),
     format!("{}-{:9>19}", key_prefix, i64::MAX), false)
};
```

### 分页实现机制

#### 1. 偏移量处理
```rust
if count < offset {
    count += 1;
    continue;  // 跳过记录，不加入结果集
}
```

#### 2. 限制处理
```rust
if results.len() >= limit as usize {
    break;     // 达到限制，停止查询
}
```

#### 3. 高效分页策略
- **小偏移量** - 直接跳过前N条记录
- **大偏移量** - 可能需要遍历大量记录，建议使用基于游标的分页
- **批量查询** - ID批量查询不受偏移量影响，性能稳定

### 查询示例

#### 基础分页查询
```json
{
  "limit": 10,
  "offset": 20,
  "sortDesc": true
}
```

#### 复合条件分页查询
```json
{
  "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS",
  "status": "ServicePending",
  "limit": 5,
  "offset": 0,
  "sortDesc": false
}
```

#### 时间范围分页查询
```json
{
  "createdAtStart": **********,
  "createdAtEnd": **********,
  "limit": 50,
  "offset": 100
}
```

#### 批量ID查询（无需分页）
```json
{
  "ids": ["id1", "id2", "id3", "id4", "id5"]
}
```

## 最佳实践建议

### 查询优化
1. **优先使用索引字段** - 根据查询需求选择有索引的字段作为过滤条件
2. **合理使用复合索引** - 多条件查询时尽量匹配复合索引
3. **避免大偏移量** - 大偏移量分页性能较差，建议使用基于时间戳的游标分页
4. **批量操作优先** - 对于已知ID的查询，使用批量ID查询性能最优

### 分页策略
1. **小数据集** - 直接使用limit/offset分页
2. **大数据集** - 使用时间戳范围 + limit进行游标分页
3. **实时数据** - 使用降序排序获取最新数据

### 索引使用
1. **单条件查询** - 使用对应的单字段索引
2. **多条件查询** - 优先匹配复合索引
3. **时间范围查询** - 结合时间戳索引和过滤条件
4. **全表扫描** - 仅在必要时使用，注意性能影响

## 客户端界面开发建议

### 数据展示
1. **表格视图** - 支持分页、排序、过滤的数据表格
2. **详情视图** - 单条记录的详细信息展示
3. **关联视图** - 显示表间关联关系（如Order与UserService）

### 查询界面
1. **过滤器** - 为每个表提供对应的过滤条件输入
2. **分页控件** - 支持页码跳转和页面大小设置
3. **排序控件** - 提供升序/降序切换
4. **导出功能** - 支持查询结果导出

### 性能优化
1. **懒加载** - 大数据集采用懒加载策略
2. **缓存机制** - 缓存常用查询结果
3. **批量操作** - 支持批量查询和操作
4. **实时更新** - 考虑数据实时性需求
