impl VCloudDB {
    /// Create a new provider from JSON string
    pub fn insert_provider(&mut self, provider_json: String) -> anyhow::Result<String> {
        let mut provider: Provider = serde_json::from_str(&provider_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse provider JSON: {}", e))?;
        
        // Validate required fields
        if provider._id.is_empty() {
            return Err(anyhow::anyhow!("Provider ID cannot be empty"));
        }

        if self.providers.contains(&provider._id) {
            return Err(anyhow::anyhow!("Provider with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_provider_timestamp_handling(&mut provider);

        // Ensure deleted_at is 0 for new providers
        provider.deleted_at = 0;

        self.providers.insert(&provider._id, &provider);
        Ok(provider._id)
    }

    /// Create multiple providers from JSON array string
    pub fn insert_many_provider(&mut self, providers_json: String) -> anyhow::Result<String> {
        let providers: Vec<Provider> = serde_json::from_str(&providers_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse providers JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for mut provider in providers {
            // Validate required fields
            if provider._id.is_empty() {
                result.errors.push("Provider ID cannot be empty".to_string());
                continue;
            }

            if self.providers.contains(&provider._id) {
                result.errors.push(format!("Provider with ID '{}' already exists", provider._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_provider_timestamp_handling(&mut provider);

            // Ensure deleted_at is 0 for new providers
            provider.deleted_at = 0;

            // Insert the provider
            self.providers.insert(&provider._id, &provider);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single provider by ID
    pub fn get_provider(&self, id: String) -> anyhow::Result<String> {
        let provider = self.providers.get(&id);
        match provider {
            Some(provider) => Ok(serde_json::to_string(&provider)?),
            None => Err(anyhow::anyhow!("not found")),
        }
    }

    /// Update an existing provider from JSON string
    pub fn update_provider(&mut self, provider_json: String) -> anyhow::Result<()> {
        let mut provider: Provider = serde_json::from_str(&provider_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse provider JSON: {}", e))?;

        // Validate required fields
        if provider._id.is_empty() {
            return Err(anyhow::anyhow!("Provider ID cannot be empty"));
        }

        if !self.providers.contains(&provider._id) {
            return Err(anyhow::anyhow!("Provider not found"));
        }

        // Apply timestamp handling logic for updates
        if provider.updated_at == 0 {
            provider.updated_at = self.get_current_timestamp();
        }

        self.providers.insert(&provider._id, &provider);
        Ok(())
    }

    /// Apply timestamp handling logic based on input values
    pub(crate) fn apply_provider_timestamp_handling(&self, provider: &mut Provider) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if provider.created_at == 0 {
            provider.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if provider.updated_at == 0 {
            provider.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update multiple providers with partial field updates
    pub fn update_many_provider(&mut self, update_json: String) -> anyhow::Result<String> {
        let params: ProviderUpdate = serde_json::from_str(&update_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse ProviderUpdate JSON: {}", e))?;

        // Find providers matching the filter criteria
        let mut providers_to_update = Vec::new();

        if let Some(ref ids) = params.filter.ids {
            for id in ids {
                if let Some(provider) = self.providers.get(id) {
                    if self.matches_provider_filters(&provider, &params.filter) {
                        providers_to_update.push(provider);
                    }
                }
            }
        } else {
            let mut iter = self.providers.index("providers_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let provider = iter.value().map_err(|e| anyhow::anyhow!("Failed to get provider: {}", e))?;
                if self.matches_provider_filters(&provider, &params.filter) {
                    providers_to_update.push(provider);
                }
            }
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Update each matching provider
        for mut provider in providers_to_update {
            // Apply partial updates from update_data
            if let Some(name) = params.update_data.get("name").and_then(|v| v.as_str()) {
                if !name.is_empty() {
                    provider.name = name.to_string();
                }
            }
            if let Some(wallet_address) = params.update_data.get("walletAddress").and_then(|v| v.as_str()) {
                if !wallet_address.is_empty() {
                    provider.wallet_address = wallet_address.to_string();
                }
            }
            if let Some(publickey) = params.update_data.get("publickey").and_then(|v| v.as_str()) {
                if !publickey.is_empty() {
                    provider.publickey = publickey.to_string();
                }
            }
            if let Some(sign_address) = params.update_data.get("signAddress").and_then(|v| v.as_str()) {
                if !sign_address.is_empty() {
                    provider.sign_address = sign_address.to_string();
                }
            }
            if let Some(api_host) = params.update_data.get("apiHost").and_then(|v| v.as_str()) {
                if !api_host.is_empty() {
                    provider.api_host = api_host.to_string();
                }
            }

            // Update timestamp
            provider.updated_at = self.get_current_timestamp();

            // Save the updated provider
            self.providers.insert(&provider._id, &provider);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Bulk write operations for providers
    pub fn bulk_write_provider(&mut self, operations_json: String) -> anyhow::Result<String> {
        let operations: Vec<ProviderBulkWriteOperation> = serde_json::from_str(&operations_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse bulk write operations JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<Provider>(data) {
                            Ok(mut provider) => {
                                if provider._id.is_empty() {
                                    result.errors.push("Provider ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.providers.contains(&provider._id) {
                                    result.errors.push(format!("Provider with ID '{}' already exists", provider._id));
                                    continue;
                                }
                                self.apply_provider_timestamp_handling(&mut provider);
                                provider.deleted_at = 0;
                                self.providers.insert(&provider._id, &provider);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse provider data: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        match self.delete_many_provider(serde_json::to_string(&filter)?) {
                            Ok(delete_result_json) => {
                                if let Ok(delete_result) = serde_json::from_str::<BatchResult>(&delete_result_json) {
                                    result.deleted += delete_result.deleted;
                                    result.errors.extend(delete_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Delete operation failed: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single provider (HARD DELETE)
    pub(crate) fn delete_provider(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ProviderQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ProviderQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.providers.contains(id) {
                    self.providers.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching provider
        let mut iter = self.providers.index("providers_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let provider = iter.value().map_err(|e| anyhow::anyhow!("Failed to get provider: {}", e))?;
            if self.matches_provider_filters(&provider, &params) {
                // Hard delete: completely remove from storage
                self.providers.remove(&provider._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting providers
    pub(crate) fn delete_many_provider(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ProviderQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ProviderQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find providers matching the filter criteria
        let mut providers_to_delete = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(provider) = self.providers.get(id) {
                    if self.matches_provider_filters(&provider, &params) {
                        providers_to_delete.push(provider);
                    }
                }
            }
        } else {
            let mut iter = self.providers.index("providers_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let provider = iter.value().map_err(|e| anyhow::anyhow!("Failed to get provider: {}", e))?;
                if self.matches_provider_filters(&provider, &params) {
                    providers_to_delete.push(provider);
                }
            }
        }

        // Delete each matching provider (HARD DELETE)
        for provider in providers_to_delete {
            // Hard delete: completely remove from storage
            self.providers.remove(&provider._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query providers with comprehensive filtering, pagination, and sorting
    pub fn find_provider(&self, params_json: String) -> anyhow::Result<String> {
        let params: ProviderQueryParams = serde_json::from_str(&params_json)?;

        let mut providers = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use specific indexes when single filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch providers by IDs directly
            for id in ids {
                if let Some(provider) = self.providers.get(id) {
                    if self.matches_provider_filters(&provider, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if providers.len() >= limit as usize {
                            break;
                        }
                        providers.push(provider);
                        count += 1;
                    }
                }
            }
        } else if let Some(ref name) = params.name {
            // Use name index with efficient sorting
            self.query_provider_with_index("providers_name", name, &params, &mut providers, &mut count, limit, offset)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.query_provider_with_created_at(&params, &mut providers, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&providers)?)
    }

    /// Count providers with advanced filtering
    pub fn count_provider(&self, params_json: String) -> anyhow::Result<String> {
        let params: ProviderQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use specific indexes when single filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch providers by IDs directly
            for id in ids {
                if let Some(provider) = self.providers.get(id) {
                    if self.matches_provider_filters(&provider, &params) {
                        count += 1;
                    }
                }
            }
        } else if let Some(ref name) = params.name {
            // Use name index for efficient counting
            self.count_provider_with_index("providers_name", name, &params, &mut count)?;
        } else {
            // No specific index, iterate through all providers using created_at index
            let mut iter = self.providers.index("providers_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let provider = iter.value()?;
                if self.matches_provider_filters(&provider, &params) {
                    count += 1;
                }
            }
        }

        Ok(count.to_string())
    }

    /// Helper function to check if a provider matches the given filters
    pub(crate) fn matches_provider_filters(&self, provider: &Provider, params: &ProviderQueryParams) -> bool {
        // Skip deleted providers unless specifically querying for them
        if provider.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&provider._id) {
                return false;
            }
        }

        // Check name filter
        if let Some(ref name) = params.name {
            if provider.name != *name {
                return false;
            }
        }

        // Check wallet_address filter
        if let Some(ref wallet_address) = params.wallet_address {
            if provider.wallet_address != *wallet_address {
                return false;
            }
        }

        // Check publickey filter
        if let Some(ref publickey) = params.publickey {
            if provider.publickey != *publickey {
                return false;
            }
        }

        // Check sign_address filter
        if let Some(ref sign_address) = params.sign_address {
            if provider.sign_address != *sign_address {
                return false;
            }
        }

        // Check api_host filter
        if let Some(ref api_host) = params.api_host {
            if provider.api_host != *api_host {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if provider.created_at < start {
                return false;
            }
        }
        if let Some(end) = params.created_at_end {
            if provider.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if provider.updated_at < start {
                return false;
            }
        }
        if let Some(end) = params.updated_at_end {
            if provider.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_provider_with_created_at(
        &self,
        params: &ProviderQueryParams,
        providers: &mut Vec<Provider>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.providers.index("providers_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let provider = iter.value()?;
            if self.matches_provider_filters(&provider, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if providers.len() >= limit as usize {
                    break;
                }
                providers.push(provider);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_provider_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ProviderQueryParams,
        providers: &mut Vec<Provider>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order and index type
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (
                format!("{}-{:9>19}", key_prefix, i64::MAX),
                format!("{}-{:0>19}", key_prefix, 0),
                true,
            )
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (
                format!("{}-{:0>19}", key_prefix, 0),
                format!("{}-{:9>19}", key_prefix, i64::MAX),
                false,
            )
        };

        let mut iter = self.providers.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let provider = iter.value()?;
            if self.matches_provider_filters(&provider, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if providers.len() >= limit as usize {
                    break;
                }
                providers.push(provider);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_provider_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ProviderQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.providers.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let provider = iter.value()?;
            if self.matches_provider_filters(&provider, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}
