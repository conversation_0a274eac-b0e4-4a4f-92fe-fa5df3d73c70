#[cfg(test)]
mod cli_version_tests {
    use super::*;

    fn create_test_cli_version(version: &str) -> CliVersion {
        CliVersion {
            _id: version.to_string(),
            version: version.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            change_log: "Test change log".to_string(),
            minimal_supported: "1.0.0".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        let result = db.insert_cli_version(cli_version_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "1.0.0");
        
        // Verify CLI version was inserted
        assert!(db.cli_versions.contains(&"1.0.0".to_string()));
    }

    #[glue::test]
    fn test_get_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.1.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version first
        db.insert_cli_version(cli_version_json).unwrap();
        
        // Get CLI version
        let result = db.get_cli_version("1.1.0".to_string());
        assert!(result.is_ok());
        
        let retrieved_cli_version: CliVersion = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_cli_version._id, "1.1.0");
        assert_eq!(retrieved_cli_version.version, "1.1.0");
        assert_eq!(retrieved_cli_version.change_log, "Test change log");
    }

    #[glue::test]
    fn test_insert_many_cli_version() {
        let mut db = VCloudDB::new();
        let cli_versions = vec![
            create_test_cli_version("2.0.0"),
            create_test_cli_version("2.1.0"),
        ];
        let cli_versions_json = serde_json::to_string(&cli_versions).unwrap();
        
        let result = db.insert_many_cli_version(cli_versions_json);
        assert!(result.is_ok());
        
        // Verify both CLI versions were inserted
        assert!(db.cli_versions.contains(&"2.0.0".to_string()));
        assert!(db.cli_versions.contains(&"2.1.0".to_string()));
    }

    #[glue::test]
    fn test_find_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version1 = create_test_cli_version("3.0.0");
        let mut cli_version2 = create_test_cli_version("3.1.0");
        cli_version2.minimal_supported = "3.0.0".to_string();
        
        // Insert CLI versions
        db.insert_cli_version(serde_json::to_string(&cli_version1).unwrap()).unwrap();
        db.insert_cli_version(serde_json::to_string(&cli_version2).unwrap()).unwrap();
        
        // Find all CLI versions
        let query_params = CliVersionQueryParams {
            version: None,
            minimal_supported: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_cli_version(query_json);
        assert!(result.is_ok());
        
        let found_cli_versions: Vec<CliVersion> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_cli_versions.len(), 2);
    }

    #[glue::test]
    fn test_count_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version1 = create_test_cli_version("4.0.0");
        let cli_version2 = create_test_cli_version("4.1.0");
        
        // Insert CLI versions
        db.insert_cli_version(serde_json::to_string(&cli_version1).unwrap()).unwrap();
        db.insert_cli_version(serde_json::to_string(&cli_version2).unwrap()).unwrap();
        
        // Count all CLI versions
        let query_params = CliVersionQueryParams {
            version: None,
            minimal_supported: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_cli_version(query_json);
        assert!(result.is_ok());
        
        let count: u64 = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(count, 2);
    }

    #[glue::test]
    fn test_update_cli_version() {
        let mut db = VCloudDB::new();
        let mut cli_version = create_test_cli_version("5.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version first
        db.insert_cli_version(cli_version_json).unwrap();
        
        // Update CLI version
        cli_version.change_log = "Updated change log".to_string();
        cli_version.minimal_supported = "5.0.0".to_string();
        let updated_json = serde_json::to_string(&cli_version).unwrap();
        
        let result = db.update_cli_version(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_cli_version("5.0.0".to_string()).unwrap();
        let retrieved_cli_version: CliVersion = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_cli_version.change_log, "Updated change log");
        assert_eq!(retrieved_cli_version.minimal_supported, "5.0.0");
    }

    #[glue::test]
    fn test_delete_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("6.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version first
        db.insert_cli_version(cli_version_json).unwrap();
        
        // Verify CLI version exists
        assert!(db.cli_versions.contains(&"6.0.0".to_string()));
        
        // Delete CLI version
        let query_params = CliVersionQueryParams {
            version: Some("6.0.0".to_string()),
            minimal_supported: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.delete_cli_version(query_json);
        assert!(result.is_ok());
        
        // Verify CLI version was deleted
        assert!(!db.cli_versions.contains(&"6.0.0".to_string()));
    }

    #[glue::test]
    fn test_delete_many_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version1 = create_test_cli_version("7.0.0");
        let cli_version2 = create_test_cli_version("7.1.0");
        
        // Insert CLI versions
        db.insert_cli_version(serde_json::to_string(&cli_version1).unwrap()).unwrap();
        db.insert_cli_version(serde_json::to_string(&cli_version2).unwrap()).unwrap();
        
        // Verify CLI versions exist
        assert!(db.cli_versions.contains(&"7.0.0".to_string()));
        assert!(db.cli_versions.contains(&"7.1.0".to_string()));
        
        // Delete all CLI versions with minimal_supported "1.0.0"
        let query_params = CliVersionQueryParams {
            version: None,
            minimal_supported: Some("1.0.0".to_string()),
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.delete_many_cli_version(query_json);
        assert!(result.is_ok());
        
        // Verify CLI versions were deleted
        assert!(!db.cli_versions.contains(&"7.0.0".to_string()));
        assert!(!db.cli_versions.contains(&"7.1.0".to_string()));
    }

    #[glue::test]
    fn test_update_many_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version1 = create_test_cli_version("8.0.0");
        let cli_version2 = create_test_cli_version("8.1.0");
        
        // Insert CLI versions
        db.insert_cli_version(serde_json::to_string(&cli_version1).unwrap()).unwrap();
        db.insert_cli_version(serde_json::to_string(&cli_version2).unwrap()).unwrap();
        
        // Update all CLI versions with minimal_supported "1.0.0"
        let filter = CliVersionQueryParams {
            version: None,
            minimal_supported: Some("1.0.0".to_string()),
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let update_data = serde_json::json!({
            "changeLog": "Batch updated change log"
        });
        
        let update_params = CliVersionUpdate {
            filter,
            update_data,
        };
        let update_json = serde_json::to_string(&update_params).unwrap();
        
        let result = db.update_many_cli_version(update_json);
        assert!(result.is_ok());
        
        // Verify updates
        let retrieved1 = db.get_cli_version("8.0.0".to_string()).unwrap();
        let retrieved_cli_version1: CliVersion = serde_json::from_str(&retrieved1).unwrap();
        assert_eq!(retrieved_cli_version1.change_log, "Batch updated change log");
        
        let retrieved2 = db.get_cli_version("8.1.0".to_string()).unwrap();
        let retrieved_cli_version2: CliVersion = serde_json::from_str(&retrieved2).unwrap();
        assert_eq!(retrieved_cli_version2.change_log, "Batch updated change log");
    }

    #[glue::test]
    fn test_bulk_write_cli_version() {
        let mut db = VCloudDB::new();
        
        // Create bulk write operations
        let operations = vec![
            CliVersionBulkWriteOperation {
                operation_type: "insert".to_string(),
                filter: None,
                data: Some(serde_json::json!([
                    create_test_cli_version("9.0.0"),
                    create_test_cli_version("9.1.0")
                ])),
            },
            CliVersionBulkWriteOperation {
                operation_type: "update".to_string(),
                filter: Some(CliVersionQueryParams {
                    version: Some("9.0.0".to_string()),
                    minimal_supported: None,
                    created_at_start: None,
                    created_at_end: None,
                    updated_at_start: None,
                    updated_at_end: None,
                    offset: None,
                    limit: None,
                    sort_by: None,
                    sort_desc: None,
                }),
                data: Some(serde_json::json!({
                    "changeLog": "Bulk write updated"
                })),
            },
        ];
        
        let operations_json = serde_json::to_string(&operations).unwrap();
        let result = db.bulk_write_cli_version(operations_json);
        assert!(result.is_ok());
        
        // Verify operations
        assert!(db.cli_versions.contains(&"9.0.0".to_string()));
        assert!(db.cli_versions.contains(&"9.1.0".to_string()));
        
        let retrieved = db.get_cli_version("9.0.0".to_string()).unwrap();
        let retrieved_cli_version: CliVersion = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_cli_version.change_log, "Bulk write updated");
    }
}
