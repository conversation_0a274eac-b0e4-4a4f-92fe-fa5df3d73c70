#!/usr/bin/env python3
"""
Simple test script to verify the new table implementations
"""
import json
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from vm import ContractTester
    
    def test_new_tables():
        print("Initializing contract...")
        vcloudClient = ContractTester(wasmName="vcloud_db")
        vcloudClient.constructor()
        print("✓ Contract initialized successfully")
        
        # Test CLI Version
        print("\n=== Testing CLI Version ===")
        cli_version = {
            "version": "test_1.0.0",
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "changeLog": "Test release",
            "minimalSupported": "1.0.0"
        }
        cli_version_json = json.dumps(cli_version)
        
        # Insert CLI version
        result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
        if err:
            print(f"✗ CLI version insert failed: {err}")
            return False
        print(f"✓ CLI version insert success: {result}")
        
        # Get CLI version
        result, err = vcloudClient.executeReadOnly("get", str, "cli_version", "test_1.0.0")
        if err:
            print(f"✗ CLI version get failed: {err}")
            return False
        print(f"✓ CLI version get success")
        
        # Test Currency
        print("\n=== Testing Currency ===")
        currency = {
            "_id": "test_btc",
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "nameOrId": "Bitcoin",
            "contractId": "0xtest_btc",
            "symbolName": "BTC",
            "contractType": "ERC20",
            "unit": 8,
            "exchangeRate": 50000.0
        }
        currency_json = json.dumps(currency)
        
        # Insert currency
        result, err = vcloudClient.execute("insert", str, "currency", currency_json)
        if err:
            print(f"✗ Currency insert failed: {err}")
            return False
        print(f"✓ Currency insert success: {result}")
        
        # Get currency
        result, err = vcloudClient.executeReadOnly("get", str, "currency", "test_btc")
        if err:
            print(f"✗ Currency get failed: {err}")
            return False
        print(f"✓ Currency get success")
        
        # Test Order Service
        print("\n=== Testing Order Service ===")
        order_service = {
            "_id": "test_os",
            "createdAt": 0,
            "updatedAt": 0,
            "deletedAt": 0,
            "orderID": "test_order",
            "userServiceID": "test_user_service",
            "orderStatus": "pending",
            "orderType": "compute"
        }
        order_service_json = json.dumps(order_service)
        
        # Insert order service
        result, err = vcloudClient.execute("insert", str, "order_service", order_service_json)
        if err:
            print(f"✗ Order service insert failed: {err}")
            return False
        print(f"✓ Order service insert success: {result}")
        
        # Get order service
        result, err = vcloudClient.executeReadOnly("get", str, "order_service", "test_os")
        if err:
            print(f"✗ Order service get failed: {err}")
            return False
        print(f"✓ Order service get success")
        
        # Test find operations
        print("\n=== Testing Find Operations ===")
        
        # Find CLI versions
        filter_params = {"limit": 10, "offset": 0}
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "cli_version", filter_json)
        if err:
            print(f"✗ CLI version find failed: {err}")
            return False
        found_cli_versions = json.loads(result)
        print(f"✓ CLI version find success: found {len(found_cli_versions)} versions")
        
        # Find currencies
        result, err = vcloudClient.executeReadOnly("find", str, "currency", filter_json)
        if err:
            print(f"✗ Currency find failed: {err}")
            return False
        found_currencies = json.loads(result)
        print(f"✓ Currency find success: found {len(found_currencies)} currencies")
        
        # Find order services
        result, err = vcloudClient.executeReadOnly("find", str, "order_service", filter_json)
        if err:
            print(f"✗ Order service find failed: {err}")
            return False
        found_order_services = json.loads(result)
        print(f"✓ Order service find success: found {len(found_order_services)} order services")
        
        # Test count operations
        print("\n=== Testing Count Operations ===")
        
        # Count CLI versions
        result, err = vcloudClient.executeReadOnly("count", str, "cli_version", filter_json)
        if err:
            print(f"✗ CLI version count failed: {err}")
            return False
        count_result = json.loads(result)
        print(f"✓ CLI version count success: {count_result['count']} versions")
        
        # Count order services
        result, err = vcloudClient.executeReadOnly("count", str, "order_service", filter_json)
        if err:
            print(f"✗ Order service count failed: {err}")
            return False
        count_result = json.loads(result)
        print(f"✓ Order service count success: {count_result['count']} order services")
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    if __name__ == "__main__":
        success = test_new_tables()
        sys.exit(0 if success else 1)
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
