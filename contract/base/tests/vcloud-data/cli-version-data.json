[{"_id": "63b3993c81e05082d09920b4", "createdAt": 1672714556, "updatedAt": 1672714556, "deletedAt": 0, "version": "0.2.0", "changeLog": "Basic functionality", "minimalSupported": "0.1.0"}, {"_id": "6497e6be622a8a2d46f5af21", "createdAt": 1687675719, "updatedAt": 1687675719, "deletedAt": 0, "version": "0.3.0", "changeLog": "1. Supported containers in a pod deployment\n2. Fixed some bugs", "minimalSupported": "0.2.0"}, {"_id": "64b60aa6622a8a2d46f5af30", "createdAt": 1689651836, "updatedAt": 1689651836, "deletedAt": 0, "version": "0.4.0", "changeLog": "1. Supported multiple domains", "minimalSupported": "0.2.0"}, {"_id": "64db33fa4a0acf00a9c82229", "createdAt": 1692087123, "updatedAt": 1692087123, "deletedAt": 0, "version": "0.5.0", "changeLog": "1. Supported multiple regions", "minimalSupported": "0.5.0"}, {"_id": "65090ca72b4f67e4a2953a8d", "createdAt": 1695091790, "updatedAt": 1695091790, "deletedAt": 0, "version": "0.6.0", "changeLog": "1. Supported user config\n2. Update environment variable", "minimalSupported": "0.5.0"}, {"_id": "6526087b2b4f67e4a2953a9b", "createdAt": 1696991259, "updatedAt": 1696991259, "deletedAt": 0, "version": "0.7.0", "changeLog": "1. Supported github container registry(ghcr) image\n2. Supported Docker hub private image", "minimalSupported": "0.7.0"}, {"_id": "655b0fd62b4f67e4a2953ab5", "createdAt": 1700466393, "updatedAt": 1700466393, "deletedAt": 0, "version": "0.8.0", "changeLog": "1. Add an operation Redeployment\n2. add user service metrics monitroing", "minimalSupported": "0.7.0"}, {"_id": "658a935c2b4f67e4a2953abe", "createdAt": 1703580387, "updatedAt": 1703580387, "deletedAt": 0, "version": "0.9.0", "changeLog": "1. Add a description and a label to user service\n2. Add stop/start to user service", "minimalSupported": "0.7.0"}, {"_id": "658e28f82b4f67e4a2953ac7", "createdAt": 1703815388, "updatedAt": 1703815388, "deletedAt": 0, "version": "0.9.1", "changeLog": "Fix a bug", "minimalSupported": "0.7.0"}, {"_id": "65a7705e2b4f67e4a2953ae9", "createdAt": 1705472026, "updatedAt": 1705472026, "deletedAt": 0, "version": "0.10.0", "changeLog": "Add Email notifications", "minimalSupported": "0.9.2"}]