[{"_id": "684108c2a4e5f6b2d4c2dad5", "createdAt": **********, "updatedAt": **********, "duration": 24, "endAt": 86400, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "68410844a4e5f6b2d4c2dad3", "createdAt": **********, "updatedAt": **********, "duration": 3, "endAt": 10800, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "684104b75381c50d560fdd64", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "684100695381c50d560fdd62", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "684015636c2a6f666e45d3ab", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ"}, {"_id": "683fec21f7498f3f73ef0572", "createdAt": **********, "updatedAt": **********, "duration": 720, "endAt": 2592000, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683febf59a7af9abfdfbafd2", "createdAt": **********, "updatedAt": **********, "duration": 720, "endAt": 2592000, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683febd89a7af9abfdfbafd0", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683fbc0eff9a6330bb9aa0b1", "createdAt": **********, "updatedAt": **********, "duration": 168, "endAt": 604800, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "labelHash": "", "amount": 0.42, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U"}, {"_id": "683f9d7b6e8a8f579a2e960f", "createdAt": **********, "updatedAt": **********, "duration": 1, "endAt": 3600, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port"}, "labelHash": "", "amount": 0.0025, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683f9cc90e331b0e018fa6f2", "createdAt": **********, "updatedAt": **********, "duration": 720, "endAt": 2592000, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "labelHash": "", "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683f9c656e8a8f579a2e960d", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683f9af46e8a8f579a2e960b", "createdAt": **********, "updatedAt": **********, "duration": 720, "endAt": 2592000, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 1.8, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683eb91ce6adb1876d007a95", "createdAt": **********, "updatedAt": **********, "duration": 24, "endAt": 86400, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"resourceUnit": "1-Unit-Resource", "region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No"}, "labelHash": "", "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683eb7eae6adb1876d007a93", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "683e93e3acf533a979ce8bad", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "labelHash": "", "amount": 0.005, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "6838319a1f84d645d975ad01", "createdAt": **********, "updatedAt": **********, "duration": 1, "endAt": 3600, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "labelHash": "", "amount": 0.0025, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt"}, {"_id": "68382f4f1f84d645d975acff", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt"}, {"_id": "6830496c2422e35551ee4e01", "createdAt": **********, "updatedAt": **********, "duration": 168, "endAt": 604800, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "createdAddr": "ATwrV4HDPUURVU8RT15yJN2qZhG7k1VEgMa", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "North America", "portSpecification": "User Specified Service Port", "persistStorage": "Yes", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.42, "publicKey": "WcD9xYc22Ae8ii4gdYreAb28oT6wMewXN3XY76Dc9H8"}, {"_id": "683021d1fa387af8b65e7d5c", "createdAt": **********, "updatedAt": **********, "duration": 1, "endAt": 3600, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.0025, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ"}]