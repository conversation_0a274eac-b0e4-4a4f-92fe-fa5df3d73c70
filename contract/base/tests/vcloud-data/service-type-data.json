[{"_id": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "name": "Container Service", "provider": "v-kube-service", "refundable": true, "categoryID": "3831b864-9ed7-4bd5-9360-6baddbc52b59", "category": "Container-Service", "serviceOptions": {"portSpecification": ["User Specified Service Port", "System Random Map Port"], "resourceUnit": ["1-Unit-Resource", "2-Unit-Resource", "4-Unit-Resource", "8-Unit-Resource", "12-Unit-Resource", "16-Unit-Resource", "20-Unit-Resource", "24-Unit-Resource", "28-Unit-Resource"], "persistStorage": ["No", "Yes"], "region": ["Europe", "North America"]}, "description": "Documentation for user to use this service: https://docs.vcloud.systems", "apiHost": "https://test.vkube.vcloud.systems", "durationToPrice": [{"price": 0.0025, "chargingOptions": {"resourceUnit": "1-Unit-Resource"}, "duration": {}}, {"price": 0.005, "chargingOptions": {"resourceUnit": "2-Unit-Resource"}, "duration": {}}, {"price": 0.01, "chargingOptions": {"resourceUnit": "4-Unit-Resource"}, "duration": {}}, {"price": 0.02, "chargingOptions": {"resourceUnit": "8-Unit-Resource"}, "duration": {}}, {"price": 0.03, "chargingOptions": {"resourceUnit": "12-Unit-Resource"}, "duration": {}}, {"price": 0.04, "chargingOptions": {"resourceUnit": "16-Unit-Resource"}, "duration": {}}, {"price": 0.05, "chargingOptions": {"resourceUnit": "20-Unit-Resource"}, "duration": {}}, {"price": 0.06, "chargingOptions": {"resourceUnit": "24-Unit-Resource"}, "duration": {}}, {"price": 0.07, "chargingOptions": {"resourceUnit": "28-Unit-Resource"}, "duration": {}}], "serviceAPI": {"start": "local http = require(\"VsysHttp\")\nlocal state = require(\"LuaRunState\")\nlocal regionMapURL = {\n    [\"North America\"] = \"https://north-america.test.vkube.vcloud.systems/api/v1/secret\",\n    [\"Europe\"] = \"https://europe.test.vkube.vcloud.systems/api/v1/secret\",\n}\nstart = function(params)\n    state.preventDefault()\n    state.tokenCreated()\n    local regionMapUserServices = {}\n    for _, userService in ipairs(params) do\n        local region = userService.options.region\n        if region == nil or regionMapURL[region] == nil then\n            error(\"There is at least one or more userService without a valid region\", 1)\n        end\n        if regionMapUserServices[region] == nil then\n            regionMapUserServices[region] = {}\n        end\n        regionMapUserServices[region][#regionMapUserServices[region] + 1] = userService\n    end\n\n    local secrets = {}\n    for k, v in pairs(regionMapUserServices) do\n        local tmpTable = {}\n        tmpTable[\"userServices\"] = v\n        local ok, resp = pcall(http.post, regionMapURL[k], tmpTable)\n        if ok then\n            for i, val in pairs(resp[\"secrets\"]) do\n                secrets[#secrets + 1] = val\n            end\n        else\n            for i, _ in pairs(v) do\n                local t = {\n                    id = \"\",\n                    secrets = \"\",\n                    ip = \"\"\n                }\n                secrets[#secrets + 1] = t\n            end\n        end\n    end\n    local res = {}\n    res[\"secrets\"] = secrets\n    return res\nend", "stop": "local http = require(\"VsysHttp\")\nregionMapURL = {}\nregionMapURL[\"North America\"] = \"https://north-america.test.vkube.vcloud.systems/api/v1/k8s/remove/pod\"\nregionMapURL[\"Europe\"] = \"https://europe.test.vkube.vcloud.systems/api/v1/k8s/remove/pod\"\nregionMapURL[\"Asia\"] = \"https://asia.test.vkube.vcloud.systems/api/v1/k8s/remove/pod\"\nstop = function(params)\n    a = {} \t\n    a[\"userServices\"] = params[\"userServices\"]\n    regionMapUserServices = {}\n    for k,v in pairs(a[\"userServices\"]) do\n        b = v[\"options\"] \n        if b[\"region\"] == nil then \n            error(string.format(\"there is at least one or more userService without region\"),1)\n            break\n        end\n        regionMapUserServices[b[\"region\"]] = {}\n    end\n    for k,v in pairs(a[\"userServices\"]) do\n        b = v[\"options\"]\n        svc = {}\n        svc[\"userServiceID\"] = v[\"id\"]\n        svc[\"createdAt\"] = v[\"createdAt\"]\n        svc[\"status\"]    = v[\"status\"]\n        regionLen = #regionMapUserServices[b[\"region\"]]\n        regionMapUserServices[b[\"region\"]][regionLen+1] = svc\n    end\n    judgeConfig(regionMapURL,regionMapUserServices)\n    res = {}\n    for k,uSvcs in pairs(regionMapUserServices) do\n        repeat\n            tmpTable = {}\n            tmpTable[\"userServices\"] = uSvcs\n            local ok,resp = pcall(http.post,regionMapURL[k],tmpTable)\n            if not ok then\n                res[k] = string.format(\"post request to the backend that handle %s region err\",k)\n                break\n            end  \n            res[k] = resp[\"message\"]\n        until true\n    end\n    return res\nend\nfunction judgeConfig(t1,t2)\n    for k,v in pairs(t2) do\n        if (t1[k] == nil) then\n            error(string.format(\"The map of region has a not existed key\"),1)\n            break\n        end\n    end\nend", "refund": "", "normal": {}, "secret": {}, "sendNotice": "local vsysHttp = require(\"VsysHttp\")\nlocal targetUrl = \"https://europe.test.vkube.vcloud.systems/api/v1/k8s/notice\"\nsendNotice = function(params)\n    local ok, resp = pcall(vsysHttp.post, targetUrl, params)\n    if ok then\n        if resp == nil then\n            return \"resp nil\"\n        end\n        return resp\n    else\n        return \"send notice failed\"\n    end\nend"}, "serviceOptionDesc": {"resourceUnit": {"1-Unit-Resource": "1 host port and 1 Gi pod memory", "2-Unit-Resource": "2 host ports and 2 Gi pod memory", "4-Unit-Resource": "4 host ports and 4 Gi pod memory", "8-Unit-Resource": "8 host ports and 8 Gi pod memory", "16-Unit-Resource": "16 host ports and 16 Gi pod memory"}}}, {"_id": "67c17e030f86c60edbc5f547", "name": "Log Host", "apiHost": "https://test.vkube.vcloud.systems", "category": "Log Service", "categoryID": "67b5b5eb68c11853fd59eed5", "createdAt": **********, "deletedAt": 0, "description": "Documentation for user to use this service: http:xxxxxxx", "durationToPrice": [{"price": 0.01, "chargingOptions": {"resourceUnit": "1-Unit-Resource"}}, {"price": 0.02, "chargingOptions": {"resourceUnit": "2-Unit-Resource"}}], "provider": "v-kube-service", "refundable": false, "serviceAPI": {"start": "local http = require(\"VsysHttp\")\nlocal state = require(\"LuaRunState\")\n\nlocal regionMapURL = {\n    [\"north_america\"] = \"http://north_america.vkube.test/api/v1/secret\",\n    [\"localhost\"] = \"http://localhost:3004/api/v1/secret\",\n    [\"europe\"] = \"http://europe.vkube.test/api/v1/secret\"\n}\n\nstart = function(params)\n    state.preventDefault()\n    state.tokenCreated()\n    local regionMapUserServices = {}\n    for _, userService in ipairs(params) do\n        local region = userService.options.region\n        if region == nil or regionMapURL[region] == nil then\n            error(\"There is at least one or more userService without a valid region\", 1)\n        end\n        if regionMapUserServices[region] == nil then\n            regionMapUserServices[region] = {}\n        end\n        regionMapUserServices[region][#regionMapUserServices[region] + 1] = userService\n    end\n\n    local secrets = {}\n    for k, v in pairs(regionMapUserServices) do\n        local tmpTable = {}\n        tmpTable[\"userServices\"] = v\n        local ok, resp = pcall(http.post, regionMapURL[k], tmpTable)\n        if ok then\n            for i, val in pairs(resp[\"secrets\"]) do\n                secrets[#secrets + 1] = val\n            end\n        else\n            for i, _ in pairs(v) do\n                local t = {\n                    id = \"\",\n                    secrets = \"\",\n                    ip = \"\"\n                }\n                secrets[#secrets + 1] = t\n            end\n        end\n    end\n    local res = {}\n    res[\"secrets\"] = secrets\n    return res\nend", "stop": "local http = require(\"VsysHttp\")\nlocal regionMapURL = {\n    [\"localhost\"] = \"http://localhost:3004/api/v1/logging/tenant/remove\",\n    [\"north_america\"] = \"http://north_america.vkube.test/api/v1/logging/tenant/remove\",\n    [\"europe\"] = \"http://europe.vkube.test/api/v1/logging/tenant/remove\"\n}\nstop = function(params)\n\n    local regionMapUserServices = {}\n    for _,userService in pairs(params) do\n        local region = userService.options.region\n        if region == nil or regionMapURL[region] == nil then\n            error(\"There is at least one or more userService without a valid region\", 1)\n        end\n        if regionMapUserServices[region] == nil then\n            regionMapUserServices[region] = {}\n        end\n        local svc = {}\n        svc[\"userServiceID\"] = userService[\"id\"]\n        svc[\"createdAt\"] = userService[\"createdAt\"]\n        svc[\"status\"] = userService[\"status\"]\n        regionMapUserServices[region][#regionMapUserServices[region] + 1] = svc\n    end\n    local res = {}\n    for k,uSvcs in pairs(regionMapUserServices) do\n        local tmpTable = {}\n        tmpTable[\"userServices\"] = uSvcs\n        local ok,resp = pcall(http.post,regionMapURL[k],tmpTable)\n        if not ok then\n            res[k] = string.format(\"post request to the backend that handle %s region err\",k)\n            break\n        end  \n        res[k] = resp[\"message\"]\n    end\n    return res\nend"}, "serviceOptionDesc": {"resourceUnit": {"1-Unit-Resource": "Host 4Gi log", "2-Unit-Resource": "Host 8Gi log"}}, "serviceOptions": {"region": ["North America", "Europe"], "resourceUnit": ["1-Unit-Resource", "2-Unit-Resource"]}, "updatedAt": **********}]