[{"type": "insert", "data": {"_id": "json_bulk_order_001", "createdAt": **********, "updatedAt": **********, "type": "OrderTypeRefund", "amount": 1.22, "amountPaid": 0, "provider": "v-kube-service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "status": "OrderPending", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "", "userServiceIDs": ["json_bulk_user_service_001"], "items": [{"userServiceID": "json_bulk_user_service_001", "duration": 488, "amount": 1.22}]}}, {"type": "insert", "data": {"_id": "json_bulk_order_002", "createdAt": **********, "updatedAt": **********, "type": "OrderTypeRenew", "amount": 1.2, "amountPaid": 0, "provider": "v-kube-service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "status": "OrderPending", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "", "userServiceIDs": ["json_bulk_user_service_002"], "items": [{"userServiceID": "json_bulk_user_service_002", "duration": 480, "amount": 1.2}]}}]