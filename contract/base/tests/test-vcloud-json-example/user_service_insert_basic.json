{"_id": "json_test_user_service_insert_001", "createdAt": **********, "updatedAt": **********, "duration": 24, "endAt": 86400, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.06, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}