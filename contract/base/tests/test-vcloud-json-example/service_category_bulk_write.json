[{"type": "insert", "data": {"_id": "sc_bulk_insert_001", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-bulk-service", "name": "Bulk Insert Service", "serviceOptions": {"type": ["bulk", "test"]}, "description": "Service category created via bulk write", "name2ID": {"bulk": "bulk_001"}, "apiHost": "api.bulk.service.com"}}, {"type": "update", "filter": {"provider": "v-analytics-service"}, "data": {"_id": "", "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "provider": "", "name": "", "serviceOptions": {}, "description": "Updated via bulk write operation", "name2ID": {}, "apiHost": ""}}, {"type": "delete_many", "filter": {"provider": "v-security-service"}}]