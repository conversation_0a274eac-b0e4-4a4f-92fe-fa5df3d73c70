[{"_id": "json_test_user_service_many_001", "createdAt": **********, "updatedAt": **********, "duration": 3, "endAt": 10800, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "createdAddr": "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.0075, "publicKey": "GEj1Z3Jy2N7MAH3QEt2ERWjXcQesVCmRfe5pZLUHXMhp"}, {"_id": "json_test_user_service_many_002", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "createdAddr": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"region": "Europe", "portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource"}, "labelHash": "", "amount": 0.005, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ"}]