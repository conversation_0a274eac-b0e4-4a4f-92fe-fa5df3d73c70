[{"_id": "json_test_order_many_001", "createdAt": **********, "updatedAt": **********, "type": "OrderTypePurchase", "amount": 0.0025, "amountPaid": 0, "provider": "v-kube-service", "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR", "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "status": "OrderPending", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "GvaJxsN4wzfxhM3v8iAA9b1u6sCi4ahtjTRaZ4SbiJ9T", "userServiceIDs": ["json_test_user_service_many_001"], "items": [{"userServiceID": "json_test_user_service_many_001", "duration": 1, "amount": 0.0025}]}, {"_id": "json_test_order_many_002", "createdAt": **********, "updatedAt": **********, "type": "OrderTypeRenew", "amount": 1.8, "amountPaid": 0, "provider": "v-kube-service", "address": "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb", "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "status": "OrderPending", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "4YJ47MDqUkBqxGMYLKpwn1AUz9kK4XdU2P9zrdR9vdEJ", "userServiceIDs": ["json_test_user_service_many_002"], "items": [{"userServiceID": "json_test_user_service_many_002", "duration": 720, "amount": 1.8}]}]