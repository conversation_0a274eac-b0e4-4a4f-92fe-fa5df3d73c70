{"_id": "json_test_order_insert_001", "createdAt": **********, "updatedAt": **********, "type": "OrderTypePurchase", "amount": 0.005, "amountPaid": 0, "provider": "v-kube-service", "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR", "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "status": "OrderPending", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "GvaJxsN4wzfxhM3v8iAA9b1u6sCi4ahtjTRaZ4SbiJ9T", "userServiceIDs": ["json_test_user_service_insert_001"], "items": [{"userServiceID": "json_test_user_service_insert_001", "duration": 2, "amount": 0.005}]}