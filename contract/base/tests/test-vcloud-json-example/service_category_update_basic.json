{"_id": "sc_684108c2a4e5f6b2d4c2dad5", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-kube-service", "name": "Updated Container Services", "serviceOptions": {"region": ["Europe", "North America", "Asia", "Australia"], "portSpecification": ["User Specified Service Port", "Auto Assigned Port"], "persistStorage": ["Yes", "No"], "resourceUnit": ["1-Unit-Resource", "2-Unit-Resource", "4-Unit-Resource", "8-Unit-Resource"]}, "description": "Updated Kubernetes-based container orchestration services with enhanced features", "name2ID": {"container": "container_001", "kubernetes": "k8s_001", "docker": "docker_001", "updated": "updated_001"}, "apiHost": "api.updated.v-kube.service.com"}