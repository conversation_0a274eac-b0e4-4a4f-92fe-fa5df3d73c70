[{"type": "insert", "data": {"_id": "json_bulk_user_service_001", "createdAt": **********, "updatedAt": **********, "duration": 1, "endAt": 3600, "status": "ServiceInactivated", "serviceActivated": false, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "createdAddr": "AUCpPEgtAkfuBF63zA28jY499rfbUDvsuFF", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "labelHash": "", "amount": 0.0025, "publicKey": "EkpuedJUPt1EcLFuRHq2jiVkQXF3KJsdUjJFE4yV6hJt"}}, {"type": "insert", "data": {"_id": "json_bulk_user_service_002", "createdAt": **********, "updatedAt": **********, "duration": 2, "endAt": 7200, "status": "ServicePending", "serviceActivated": true, "serviceActivateTS": **********, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "serviceID": "e154ff0e-7e5c-453b-b33e-f03d296e4e8d", "service": "Container Service", "address": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "createdAddr": "AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj", "provider": "v-kube-service", "providerAddress": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr", "serviceOptions": {"portSpecification": "User Specified Service Port", "persistStorage": "No", "resourceUnit": "1-Unit-Resource", "region": "Europe"}, "labelHash": "", "amount": 0.005, "publicKey": "RKzKv6ZFyudJ3nBPguhervM7kDri5KpjHzR9DNupJ9U"}}]