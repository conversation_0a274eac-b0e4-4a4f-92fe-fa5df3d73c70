[{"_id": "json_test_service_category_batch_001", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-batch-service-1", "name": "Batch Test Services 1", "serviceOptions": {"cpu": ["1", "2"], "memory": ["1GB", "2GB"]}, "description": "First batch test service category", "name2ID": {"batch1": "batch1_001"}, "apiHost": "api.batch1.service.com"}, {"_id": "json_test_service_category_batch_002", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-batch-service-2", "name": "Batch Test Services 2", "serviceOptions": {"storage": ["10GB", "50GB"], "backup": ["daily", "weekly"]}, "description": "Second batch test service category", "name2ID": {"batch2": "batch2_001"}, "apiHost": "api.batch2.service.com"}]