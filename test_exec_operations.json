[{"functionType": "bulk_write", "tableName": "service_type", "parameters": "[{\"type\":\"insert\",\"data\":{\"_id\":\"68834e1d1e3cb05ba6d09bee\",\"createdAt\":**********,\"updatedAt\":**********,\"name\":\"Container Service\",\"provider\":\"v-kube-service\",\"refundable\":true,\"categoryID\":\"68834e1c1e3cb05ba6d09bed\",\"category\":\"Container-Service\",\"serviceOptions\":{\"persistStorage\":[\"yes\",\"no\"],\"portSpecification\":[\"User Specified Service Port\",\"System Random Map Port\"],\"region\":[\"North America\",\"Asia\"],\"resourceUnit\":[\"1-Unit-Resource\",\"2-Unit-Resource\",\"4-Unit-Resource\",\"8-Unit-Resource\",\"16-Unit-Resource\"]},\"description\":\"Documentation for user to use this service: http:xxxxxxx\",\"apiHost\":\"http://localhost:3004\",\"durationToPrice\":[{\"price\":100,\"chargingOptions\":{\"resourceUnit\":\"1-Unit-Resource\"},\"duration\":{\"100\":0.9,\"200\":0.85}},{\"price\":200,\"chargingOptions\":{\"resourceUnit\":\"2-Unit-Resource\"},\"duration\":{\"100\":0.9,\"200\":0.85}},{\"price\":500,\"chargingOptions\":{\"resourceUnit\":\"4-Unit-Resource\"},\"duration\":{\"100\":0.9,\"200\":0.85}},{\"price\":800,\"chargingOptions\":{\"resourceUnit\":\"8-Unit-Resource\"},\"duration\":{\"100\":0.9,\"200\":0.85}},{\"price\":1600,\"chargingOptions\":{\"resourceUnit\":\"16-Unit-Resource\"},\"duration\":{\"100\":0.9,\"200\":0.85}}]}}]"}, {"functionType": "bulk_write", "tableName": "service_category", "parameters": "[{\"type\":\"insert\",\"data\":{\"_id\":\"68834e1c1e3cb05ba6d09bed\",\"createdAt\":**********,\"updatedAt\":**********,\"provider\":\"v-kube-service\",\"name\":\"Container-Service\",\"serviceOptions\":{\"persistStorage\":[\"yes\",\"no\"],\"portSpecification\":[\"User Specified Service Port\",\"System Random Map Port\"],\"region\":[\"North America\",\"Asia\"],\"resourceUnit\":[\"1-Unit-Resource\",\"2-Unit-Resource\",\"4-Unit-Resource\",\"8-Unit-Resource\",\"16-Unit-Resource\"]},\"description\":\"Deploy docker images to k8s cluster\",\"name2ID\":{\"Container Service\":\"68834e1d1e3cb05ba6d09bee\"},\"apiHost\":\"http://localhost:3004\"}}]"}, {"functionType": "insert", "tableName": "provider", "parameters": "{\"_id\":\"68834e1d1e3cb05ba6d09bef\",\"createdAt\":**********,\"updatedAt\":**********,\"name\":\"v-kube-service\",\"walletAddress\":\"AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj\",\"publickey\":\"9GvyW7MkF9EY5SV2X5t9VtQoDQkWbkUwbVLdTZPJp7Uv\",\"category2ID\":{\"Container-Service\":\"68834e1c1e3cb05ba6d09bed\"},\"signAddress\":\"ATsVb8kJuYGpS42caZWDVmL2BD5yuLmJhfG\",\"apiHost\":\"http://localhost:3004\"}"}]