#!/bin/bash
exec agave-validator \
   --no-os-network-limits-test \
   --identity /root/validator-keypair.json \
   --known-validator 7N<PERSON>41<PERSON>YqPefeNQEHSv1UDhYrehxin3NStELsSKCT4K2 \
   --known-validator <PERSON><PERSON><PERSON>yH3YtwcxFvQrVVJMm1JhTS4QVX7MFsX56uJLUfiZ \
   --known-validator DE1bawNcRJB9rVm3buyMVfr8mBEoyyu73NBovf2oXJsJ \
   --known-validator CakcnaRDHka2gXyfbEd2d3xsvkJkqsLw2akB3zsN1D2S \
   --full-rpc-api \
   --enable-rpc-transaction-history \
   --no-voting \
   --ledger /mnt/solana-data/ledger \
   --accounts /mnt/solana-data/accounts \
   --log /root/solana-rpc.log \
   --rpc-port 8899 \
   --rpc-bind-address 0.0.0.0 \
   --private-rpc \
   --dynamic-port-range 8000-8020 \
   --expected-shred-version 50093 \
   --entrypoint entrypoint.mainnet-beta.solana.com:8001 \
   --entrypoint entrypoint2.mainnet-beta.solana.com:8001 \
   --entrypoint entrypoint3.mainnet-beta.solana.com:8001 \
   --entrypoint entrypoint4.mainnet-beta.solana.com:8001 \
   --entrypoint entrypoint5.mainnet-beta.solana.com:8001 \
   --expected-genesis-hash 5eykt4UsFv8P8NJdTREpY1vzqKqZKvdpKuc147dw2N9d \
   --limit-ledger-size ******** \
   --wal-recovery-mode skip_any_corrupted_record
   # --no-snapshot-fetch \
   #--unified-scheduler-handler-threads 32