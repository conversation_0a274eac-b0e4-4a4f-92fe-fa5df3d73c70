#!/bin/bash
function install() {
  echo "install..."
  microk8s.kubectl label namespace kube-system carina.storage.io/webhook=ignore

  if [ `microk8s.kubectl get configmap carina-csi-config -n kube-system 2>/dev/null | wc -l` == "0" ]; then
    microk8s.kubectl apply -f csi-config-map.yaml
  fi

  microk8s.kubectl apply -f crd-logicvolume.yaml
  microk8s.kubectl apply -f crd-nodestoreresource.yaml

  microk8s.kubectl apply -f csi-controller-rbac.yaml
  microk8s.kubectl apply -f csi-carina-controller.yaml
  microk8s.kubectl apply -f csi-node-rbac.yaml
  microk8s.kubectl apply -f csi-carina-node.yaml
  microk8s.kubectl apply -f carina-scheduler.yaml
  microk8s.kubectl apply -f storageclass-lvm.yaml
  microk8s.kubectl apply -f storageclass-raw.yaml
  microk8s.kubectl apply -f storageclass-host.yaml
  sleep 3s
  microk8s.kubectl apply -f prometheus-service-monitor.yaml
  echo "-------------------------------"
  echo "$ microk8s.kubectl get pods -n kube-system |grep carina"
  microk8s.kubectl get pods -n kube-system |grep carina
}


function uninstall() {
  echo "uninstall..."
  microk8s.kubectl delete secret mutatingwebhook -n kube-system
#  microk8s.kubectl delete -f csi-config-map.yaml
  microk8s.kubectl delete -f csi-controller-rbac.yaml
  microk8s.kubectl delete -f csi-carina-controller.yaml
  microk8s.kubectl delete -f csi-node-rbac.yaml
  microk8s.kubectl delete -f csi-carina-node.yaml
  microk8s.kubectl delete -f carina-scheduler.yaml
 
  microk8s.kubectl delete csr carina-controller.kube-system
  microk8s.kubectl delete configmap carina-node-storage -n kube-system
  microk8s.kubectl label namespace kube-system carina.storage.io/webhook-

  for z in `microk8s.kubectl get nodes | awk 'NR!=1 {print $1}'`; do
    microk8s.kubectl label node $z topology.carina.storage.io/node-
  done

  if [ `microk8s.kubectl get lv | wc -l` == 0 ]; then
    microk8s.kubectl delete -f crd-logicvolume.yaml
  fi
  microk8s.kubectl delete -f crd-nodestoreresource.yaml
  microk8s.kubectl delete -f storageclass-lvm.yaml
  microk8s.kubectl delete -f storageclass-raw.yaml
  microk8s.kubectl delete -f storageclass-host.yaml
  microk8s.kubectl delete -f prometheus-service-monitor.yaml
}

# Does not apply to 0.10.0->0.11.0 updates
function upgrade() {
  echo "upgrade..."
  microk8s.kubectl delete secret mutatingwebhook -n kube-system
  microk8s.kubectl delete -f csi-carina-controller.yaml
  microk8s.kubectl delete -f csi-carina-node.yaml
  microk8s.kubectl delete -f carina-scheduler.yaml
  microk8s.kubectl apply -f csi-carina-controller.yaml
  microk8s.kubectl apply -f csi-carina-node.yaml
  microk8s.kubectl apply -f carina-scheduler.yaml
}

operator=${1:-'install'}

if [ "uninstall" == $operator ]
then
  uninstall
elif [ "upgrade" == $operator ]
then
  upgrade
else
  install
fi