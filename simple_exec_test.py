#!/usr/bin/env python3
"""
简化的exec函数测试脚本 - 所有数据内嵌为字符串
"""

import json
import sys
import os

# 添加合约路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'contract', 'base'))

from vm import ContractTester

def test_exec_with_embedded_data():
    """使用内嵌JSON字符串测试exec函数"""
    
    # 初始化合约
    contract = ContractTester(wasmName="vcloud_db")
    contract.constructor()
    
    # 测试数据 - 修正后的JSON字符串
    operations_json = '''[
  {
    "functionType": "bulk_write",
    "tableName": "service_type",
    "parameters": "[{\\"type\\":\\"insert\\",\\"data\\":{\\"_id\\":\\"68834e1d1e3cb05ba6d09bee\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"name\\":\\"Container Service\\",\\"provider\\":\\"v-kube-service\\",\\"refundable\\":true,\\"categoryID\\":\\"68834e1c1e3cb05ba6d09bed\\",\\"category\\":\\"Container-Service\\",\\"serviceOptions\\":{\\"persistStorage\\":[\\"yes\\",\\"no\\"],\\"portSpecification\\":[\\"User Specified Service Port\\",\\"System Random Map Port\\"],\\"region\\":[\\"North America\\",\\"Asia\\"],\\"resourceUnit\\":[\\"1-Unit-Resource\\",\\"2-Unit-Resource\\",\\"4-Unit-Resource\\",\\"8-Unit-Resource\\",\\"16-Unit-Resource\\"]},\\"description\\":\\"Documentation for user to use this service: http:xxxxxxx\\",\\"apiHost\\":\\"http://localhost:3004\\",\\"durationToPrice\\":[{\\"price\\":100,\\"chargingOptions\\":{\\"resourceUnit\\":\\"1-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":200,\\"chargingOptions\\":{\\"resourceUnit\\":\\"2-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":500,\\"chargingOptions\\":{\\"resourceUnit\\":\\"4-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":800,\\"chargingOptions\\":{\\"resourceUnit\\":\\"8-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":1600,\\"chargingOptions\\":{\\"resourceUnit\\":\\"16-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}}]}}]"
  },
  {
    "functionType": "bulk_write",
    "tableName": "service_category",
    "parameters": "[{\\"type\\":\\"insert\\",\\"data\\":{\\"_id\\":\\"68834e1c1e3cb05ba6d09bed\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"provider\\":\\"v-kube-service\\",\\"name\\":\\"Container-Service\\",\\"serviceOptions\\":{\\"persistStorage\\":[\\"yes\\",\\"no\\"],\\"portSpecification\\":[\\"User Specified Service Port\\",\\"System Random Map Port\\"],\\"region\\":[\\"North America\\",\\"Asia\\"],\\"resourceUnit\\":[\\"1-Unit-Resource\\",\\"2-Unit-Resource\\",\\"4-Unit-Resource\\",\\"8-Unit-Resource\\",\\"16-Unit-Resource\\"]},\\"description\\":\\"Deploy docker images to k8s cluster\\",\\"name2ID\\":{\\"Container Service\\":\\"68834e1d1e3cb05ba6d09bee\\"},\\"apiHost\\":\\"http://localhost:3004\\"}}]"
  },
  {
    "functionType": "insert",
    "tableName": "provider",
    "parameters": "{\\"_id\\":\\"68834e1d1e3cb05ba6d09bef\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"name\\":\\"v-kube-service\\",\\"walletAddress\\":\\"AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj\\",\\"publickey\\":\\"9GvyW7MkF9EY5SV2X5t9VtQoDQkWbkUwbVLdTZPJp7Uv\\",\\"category2ID\\":{\\"Container-Service\\":\\"68834e1c1e3cb05ba6d09bed\\"},\\"signAddress\\":\\"ATsVb8kJuYGpS42caZWDVmL2BD5yuLmJhfG\\",\\"apiHost\\":\\"http://localhost:3004\\"}"
  }
]'''
    
    print("=== 执行批量操作 ===")
    
    # 执行exec函数
    result, err = contract.execute("exec", str, operations_json)
    
    if err is not None:
        print(f"❌ 执行失败: {err}")
        return False
    
    print(f"✅ 执行成功: {result}")
    
    # 解析结果
    response = json.loads(result)
    if response.get("message") == "ok" and len(response.get("errors", [])) == 0:
        print("✅ 所有操作都成功")
        return True
    else:
        print(f"⚠️ 有错误: {response.get('errors', [])}")
        return False

if __name__ == "__main__":
    success = test_exec_with_embedded_data()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
