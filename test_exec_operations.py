#!/usr/bin/env python3
"""
测试exec函数的vcloud_db合约函数
"""

import json
import sys
import os

# 添加合约路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'contract', 'base'))

from vm import ContractTester

def test_exec_operations():
    """测试exec操作"""
    print("=== 开始测试exec操作 ===")

    # 初始化合约
    contract = ContractTester(wasmName="vcloud_db")
    contract.constructor()
    
    # 测试数据作为字符串
    operations_json_str = '''[
  {
    "functionType": "bulk_write",
    "tableName": "service_type",
    "parameters": "[{\\"type\\":\\"insert\\",\\"data\\":{\\"_id\\":\\"68834e1d1e3cb05ba6d09bee\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"name\\":\\"Container Service\\",\\"provider\\":\\"v-kube-service\\",\\"refundable\\":true,\\"categoryID\\":\\"68834e1c1e3cb05ba6d09bed\\",\\"category\\":\\"Container-Service\\",\\"serviceOptions\\":{\\"persistStorage\\":[\\"yes\\",\\"no\\"],\\"portSpecification\\":[\\"User Specified Service Port\\",\\"System Random Map Port\\"],\\"region\\":[\\"North America\\",\\"Asia\\"],\\"resourceUnit\\":[\\"1-Unit-Resource\\",\\"2-Unit-Resource\\",\\"4-Unit-Resource\\",\\"8-Unit-Resource\\",\\"16-Unit-Resource\\"]},\\"description\\":\\"Documentation for user to use this service: http:xxxxxxx\\",\\"apiHost\\":\\"http://localhost:3004\\",\\"durationToPrice\\":[{\\"price\\":100,\\"chargingOptions\\":{\\"resourceUnit\\":\\"1-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":200,\\"chargingOptions\\":{\\"resourceUnit\\":\\"2-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":500,\\"chargingOptions\\":{\\"resourceUnit\\":\\"4-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":800,\\"chargingOptions\\":{\\"resourceUnit\\":\\"8-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}},{\\"price\\":1600,\\"chargingOptions\\":{\\"resourceUnit\\":\\"16-Unit-Resource\\"},\\"duration\\":{\\"100\\":0.9,\\"200\\":0.85}}]}}]"
  },
  {
    "functionType": "bulk_write",
    "tableName": "service_category",
    "parameters": "[{\\"type\\":\\"insert\\",\\"data\\":{\\"_id\\":\\"68834e1c1e3cb05ba6d09bed\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"provider\\":\\"v-kube-service\\",\\"name\\":\\"Container-Service\\",\\"serviceOptions\\":{\\"persistStorage\\":[\\"yes\\",\\"no\\"],\\"portSpecification\\":[\\"User Specified Service Port\\",\\"System Random Map Port\\"],\\"region\\":[\\"North America\\",\\"Asia\\"],\\"resourceUnit\\":[\\"1-Unit-Resource\\",\\"2-Unit-Resource\\",\\"4-Unit-Resource\\",\\"8-Unit-Resource\\",\\"16-Unit-Resource\\"]},\\"description\\":\\"Deploy docker images to k8s cluster\\",\\"name2ID\\":{\\"Container Service\\":\\"68834e1d1e3cb05ba6d09bee\\"},\\"apiHost\\":\\"http://localhost:3004\\"}}]"
  },
  {
    "functionType": "insert",
    "tableName": "provider",
    "parameters": "{\\"_id\\":\\"68834e1d1e3cb05ba6d09bef\\",\\"createdAt\\":**********,\\"updatedAt\\":**********,\\"name\\":\\"v-kube-service\\",\\"walletAddress\\":\\"AU2TeXVGNhZfo8ec4ktwM6KZNf9EPXGp5sj\\",\\"publickey\\":\\"9GvyW7MkF9EY5SV2X5t9VtQoDQkWbkUwbVLdTZPJp7Uv\\",\\"category2ID\\":{\\"Container-Service\\":\\"68834e1c1e3cb05ba6d09bed\\"},\\"signAddress\\":\\"ATsVb8kJuYGpS42caZWDVmL2BD5yuLmJhfG\\",\\"apiHost\\":\\"http://localhost:3004\\"}"
  }
]'''

    # 解析JSON字符串
    # operations = json.loads(operations_json_str)
    
    # print(f"加载了 {len(operations)} 个操作")

    # 一次性执行所有操作
    print(f"\n--- 执行所有操作 ---")
    try:
        # 调用exec函数，传入整个operations数组
        # result, err = contract.execute("exec", str, json.dumps(operations))
        result, err = contract.execute("exec", str, operations_json_str)
        if err is not None:
            print(f"❌ 批量操作失败: {str(err)}")
            return False
        print(f"✅ 批量操作成功")
        print(f"结果: {result}")

        # 解析结果
        response = json.loads(result)
        if response.get("message") == "ok" and len(response.get("errors", [])) == 0:
            print("✅ 所有操作都成功执行")
        else:
            print(f"⚠️ 部分操作可能失败: {response.get('errors', [])}")

    except Exception as e:
        print(f"❌ 批量操作失败: {str(e)}")
        return False
    
    print("\n=== 验证数据插入结果 ===")
    
    # 验证provider数据
    try:
        provider_result, err = contract.executeReadOnly("get", str, "provider", "68834e1d1e3cb05ba6d09bef")
        if err is not None:
            print(f"❌ Provider查询失败: {str(err)}")
        else:
            print(f"✅ Provider查询成功: {provider_result}")
    except Exception as e:
        print(f"❌ Provider查询失败: {str(e)}")

    # 验证service_category数据
    try:
        category_result, err = contract.executeReadOnly("get", str, "service_category", "68834e1c1e3cb05ba6d09bed")
        if err is not None:
            print(f"❌ ServiceCategory查询失败: {str(err)}")
        else:
            print(f"✅ ServiceCategory查询成功: {category_result}")
    except Exception as e:
        print(f"❌ ServiceCategory查询失败: {str(e)}")

    # 验证service_type数据
    try:
        service_type_result, err = contract.executeReadOnly("get", str, "service_type", "68834e1d1e3cb05ba6d09bee")
        if err is not None:
            print(f"❌ ServiceType查询失败: {str(err)}")
        else:
            print(f"✅ ServiceType查询成功: {service_type_result}")
    except Exception as e:
        print(f"❌ ServiceType查询失败: {str(e)}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_exec_operations()
    sys.exit(0 if success else 1)
